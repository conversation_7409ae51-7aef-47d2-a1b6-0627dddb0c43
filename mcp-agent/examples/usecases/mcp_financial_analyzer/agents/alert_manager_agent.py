"""
Alert Manager Agent for Financial Analysis Integration.

This module provides the AlertManagerAgent class, a comprehensive alert management system
designed for financial analysis workflows. It integrates with the MCP (Model Context Protocol)
ecosystem and the agent_develop notification services to deliver intelligent alert evaluation
and multi-channel notification capabilities.

Key Components:
- AlertManagerAgent: Main agent class inheriting from BaseAgentWrapper
- Alert data models: Structured representations of alerts and notifications
- Threshold monitoring: Configurable financial and supply chain metric evaluation
- Multi-channel delivery: Email, MQTT, and HTTP webhook notification support
- Error recovery: Circuit breaker patterns and fallback strategies
- Lifecycle management: Alert acknowledgment, resolution, and audit tracking

Architecture Overview:
The AlertManagerAgent follows the established BaseAgentWrapper pattern used throughout
the MCP financial analyzer ecosystem. It connects to the 'alert-notification' MCP server
for notification delivery and integrates with other financial analysis agents including:
- research_agent: Market data collection
- analyst_agent: Financial metric analysis
- shortage_analyzer_agent: Supply chain risk assessment

The agent processes financial data through a comprehensive pipeline:
1. Data ingestion and validation from upstream agents
2. Threshold evaluation against configurable business rules
3. Alert generation with severity classification and prioritization
4. Multi-channel notification delivery with error handling
5. Lifecycle tracking through acknowledgment and resolution

Integration Points:
- MCP Server: 'alert-notification' for HttpTool, MqttTool, EmailTool
- Schema: AlertManagementInputSchema/OutputSchema for structured I/O
- Orchestrator: Compatible with existing workflow orchestration patterns
- Configuration: Environment-based settings for thresholds and channels

Usage Example:
    ```python
    from agents.alert_manager_agent import create_alert_manager_agent

    # Create agent with custom configuration
    alert_manager = create_alert_manager_agent(
        company_name="ACME Corp",
        alert_config={
            'shortage_threshold': 0.75,
            'enabled_channels': ['mqtt', 'http'],
            'email_recipients': ['<EMAIL>', '<EMAIL>']
        }
    )

    # Process in orchestrator workflow
    orchestrator = Orchestrator(
        llm_factory=VLLMAugmentedLLM,
        available_agents=[research_agent, analyst_agent, alert_manager],
        plan_type="full"
    )
    ```

Dependencies:
- agents.base_agent_wrapper: BaseAgentWrapper base class
- schemas.agent_schemas: AlertManagementInputSchema/OutputSchema
- Standard library: asyncio, json, logging, datetime, dataclasses, enum
- Type hints: typing module for comprehensive type annotations

Author: AlertManagerAgent Implementation Team
Version: 1.0.0
License: Compatible with MCP financial analyzer ecosystem
"""

import logging
import asyncio
import json
import os
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum

from agents.base_agent_wrapper import create_enhanced_agent, BaseAgentWrapper
from schemas.agent_schemas import AlertManagementInputSchema, AlertManagementOutputSchema

# Configure logging
logger = logging.getLogger("alert_manager")


# ============================================================================
# Enums and Data Models
# ============================================================================

class AlertType(Enum):
    """Types of alerts that can be generated."""
    FINANCIAL = "financial"
    SUPPLY_CHAIN = "supply_chain"
    OPERATIONAL = "operational"


class AlertSeverity(Enum):
    """Alert severity levels for prioritization."""
    CRITICAL = "critical"  # Immediate action required
    HIGH = "high"         # Action required within 1 hour
    MEDIUM = "medium"     # Action required within 4 hours
    LOW = "low"          # Daily summary notification


class AlertStatus(Enum):
    """Alert lifecycle status."""
    PENDING = "pending"
    SENT = "sent"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    FAILED = "failed"


class NotificationChannel(Enum):
    """Available notification channels."""
    EMAIL = "email"
    MQTT = "mqtt"
    HTTP = "http"


@dataclass
class Alert:
    """Core alert data structure."""
    id: str
    alert_type: AlertType
    severity: AlertSeverity
    title: str
    message: str
    data: Dict[str, Any]
    threshold_value: float
    actual_value: float
    timestamp: datetime
    company_name: str
    status: AlertStatus = AlertStatus.PENDING
    acknowledgment_user: Optional[str] = None
    acknowledgment_time: Optional[datetime] = None
    resolution_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class NotificationDelivery:
    """Notification delivery tracking."""
    alert_id: str
    channel: NotificationChannel
    recipient: str
    status: str  # "success", "failed", "pending"
    timestamp: datetime
    error_message: Optional[str] = None
    delivery_time_ms: Optional[int] = None


@dataclass
class AlertProcessingResult:
    """Result of alert processing operation."""
    processed_alerts: List[Alert]
    notifications_sent: List[NotificationDelivery]
    errors: List[str]
    summary: str
    processing_time_ms: int


# ============================================================================
# AlertManagerAgent Class
# ============================================================================

class AlertManagerAgent(BaseAgentWrapper):
    """
    Enhanced Alert Manager Agent for financial analysis integration.

    The AlertManagerAgent is a comprehensive alert management system designed to
    monitor financial metrics, evaluate alert conditions, and deliver notifications
    through multiple channels. It integrates seamlessly with the MCP financial
    analyzer ecosystem and follows the BaseAgentWrapper pattern for consistency.

    Key Features:
    - **Intelligent Alert Evaluation**: Monitors financial metrics against configurable
      thresholds including revenue decline, profit margins, cash flow, and supply chain risks
    - **Multi-Channel Notifications**: Delivers alerts via email, MQTT, and HTTP webhooks
      with channel selection based on alert severity and business rules
    - **Alert Prioritization**: Implements sophisticated prioritization algorithms considering
      business impact, urgency, and variance from thresholds
    - **Lifecycle Management**: Tracks alerts from creation through acknowledgment to resolution
      with comprehensive audit trails and metrics
    - **Error Recovery**: Includes circuit breaker patterns, fallback channels, and graceful
      degradation to ensure system resilience
    - **Async Processing**: Uses concurrent processing with timeout and retry mechanisms
      for optimal performance and reliability

    Architecture:
    - Inherits from BaseAgentWrapper for MCP integration
    - Uses structured data models (Alert, NotificationDelivery, etc.)
    - Implements async/await patterns throughout for non-blocking operations
    - Provides comprehensive logging and metrics for monitoring

    Integration:
    - Connects to 'alert-notification' MCP server for notification delivery
    - Consumes data from research_agent, analyst_agent, and shortage_analyzer_agent
    - Compatible with existing orchestrator patterns and workflow management

    Configuration:
    - Supports dynamic threshold configuration and channel management
    - Includes business rule customization and escalation path definition
    - Provides environment-based configuration for different deployment scenarios

    Example Usage:
        ```python
        # Create alert manager with custom configuration
        alert_config = {
            'shortage_threshold': 0.8,
            'enabled_channels': ['mqtt', 'http'],
            'email_recipients': ['<EMAIL>']
        }

        alert_manager = AlertManagerAgent(
            company_name="ACME Corp",
            alert_config=alert_config
        )

        # Process financial analysis data
        input_data = AlertManagementInputSchema(
            company_name="ACME Corp",
            analysis_data=json.dumps(financial_metrics),
            shortage_data=json.dumps(supply_chain_data),
            alert_message="Evaluate financial alerts",
            channels=["email", "mqtt"],
            severity="HIGH"
        )

        result = await alert_manager.process_financial_analysis(input_data)
        ```

    Attributes:
        company_name (str): Name of the company being monitored
        alert_config (Dict[str, Any]): Configuration for thresholds and notifications
        active_alerts (Dict[str, Alert]): Currently active alerts indexed by ID
        notification_history (List[NotificationDelivery]): History of notification attempts
        processing_metrics (Dict[str, Any]): Performance and operational metrics
        error_tracking (Dict[str, Any]): Error recovery and circuit breaker state
    """

    def __init__(
        self,
        company_name: str,
        alert_config: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """
        Initialize AlertManagerAgent with configuration.

        Args:
            company_name: Name of the company being monitored
            alert_config: Alert configuration settings
            **kwargs: Additional BaseAgentWrapper parameters
        """
        # Set default configuration
        if alert_config is None:
            alert_config = {}

        # Build instruction for BaseAgentWrapper first (static method)
        instruction = self._build_instruction_static(company_name, alert_config)

        # Initialize parent BaseAgentWrapper FIRST
        super().__init__(
            name=f"alert_manager_{company_name.lower().replace(' ', '_')}",
            instruction=instruction,
            server_names=["alert-notification"],
            model="Qwen/Qwen3-32B",  # Use vLLM Qwen model for enhanced reasoning
            **kwargs
        )

        # Now set instance attributes after parent initialization
        self.company_name = company_name
        self.alert_config = self._build_default_config(alert_config)

        # Initialize alert storage and tracking
        self.active_alerts: Dict[str, Alert] = {}
        self.notification_history: List[NotificationDelivery] = []
        self.processing_metrics = {
            "alerts_generated": 0,
            "notifications_sent": 0,
            "notifications_failed": 0,
            "last_processing_time": None
        }

        # Initialize error recovery system
        self._setup_error_recovery_strategies()

        logger.info(f"AlertManagerAgent initialized for {company_name}")

    async def _send_emergency_notification(self, alert: Alert, error: Exception) -> None:
        """
        Send emergency notification when all regular channels fail.

        Args:
            alert: Alert that failed to be delivered
            error: Exception that caused the failure
        """
        try:
            # Emergency fallback - write to file or log
            emergency_message = f"""
EMERGENCY ALERT NOTIFICATION FAILURE
=====================================
Alert ID: {alert.id}
Company: {alert.company_name}
Severity: {alert.severity.value}
Title: {alert.title}
Message: {alert.message}
Timestamp: {alert.timestamp}
Error: {str(error)}

This alert failed to be delivered through all configured channels.
Manual intervention required.
"""

            # Write to emergency log file
            emergency_file = f"/tmp/emergency_alerts_{self.company_name.replace(' ', '_')}.log"
            with open(emergency_file, 'a') as f:
                f.write(emergency_message + "\n")

            logger.critical(f"Emergency notification written to {emergency_file} for alert {alert.id}")

        except Exception as e:
            logger.critical(f"Emergency notification also failed for alert {alert.id}: {e}")

    async def _check_circuit_breaker_recovery(self) -> bool:
        """
        Check if circuit breaker should be closed (recovered).

        Returns:
            True if circuit breaker should be closed, False otherwise
        """
        try:
            if not self.error_tracking.get('circuit_breaker_open', False):
                return False

            # Check if enough time has passed since circuit breaker opened
            open_time = self.error_tracking.get('circuit_breaker_open_time')
            if not open_time:
                return False

            recovery_timeout = self.error_recovery_config.get('circuit_breaker_recovery_timeout_minutes', 30)
            time_since_open = (datetime.now() - open_time).total_seconds() / 60

            if time_since_open >= recovery_timeout:
                # Close circuit breaker
                self.error_tracking['circuit_breaker_open'] = False
                self.error_tracking['consecutive_failures'] = 0
                logger.info("Circuit breaker closed - system recovered")
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking circuit breaker recovery: {e}")
            return False

    @staticmethod
    def _build_instruction_static(company_name: str, alert_config: Dict[str, Any]) -> str:
        """
        Build structured instruction for BaseAgentWrapper initialization following reference pattern.

        Args:
            company_name: Name of the company being monitored
            alert_config: Alert configuration settings

        Returns:
            Structured instruction string for the agent
        """
        # Extract configuration values with defaults
        enabled_channels = alert_config.get('enabled_channels', ['mqtt', 'http'])
        shortage_threshold = alert_config.get('shortage_threshold', 0.7)
        revenue_decline_threshold = alert_config.get('revenue_decline_threshold', 0.10)
        profit_margin_threshold = alert_config.get('profit_margin_threshold', 0.15)
        cash_flow_periods = alert_config.get('cash_flow_periods', 2)
        debt_equity_threshold = alert_config.get('debt_equity_threshold', 1.2)

        # Build structured prompt following reference implementation pattern
        background_section = f"""You are an AlertManagerAgent, a specialized financial monitoring and notification system for {company_name}.
You are designed to evaluate financial metrics, assess alert conditions, and deliver intelligent notifications through multiple channels.
You have access to the alert-notification MCP server for delivering notifications via email, MQTT, and HTTP webhooks."""

        steps_section = f"""1. Analyze the provided financial analysis data and shortage data against configured thresholds and business rules.
2. Evaluate alert conditions based on severity levels: CRITICAL (immediate action), HIGH (1 hour), MEDIUM (4 hours), LOW (daily summary).
3. Generate appropriate alerts with proper categorization, severity assessment, and actionable recommendations.
4. Select notification channels based on alert severity: CRITICAL (all channels), HIGH (email + MQTT), MEDIUM (email), LOW (MQTT only).
5. Deliver notifications through the alert-notification MCP server using appropriate tools (HttpTool, MqttTool, EmailTool).
6. Provide comprehensive alert summary with delivery status, metrics, and recommended actions."""

        output_instructions_section = f"""1. Always provide detailed reasoning for alert evaluation decisions and severity classifications.
2. Include specific metric values, threshold comparisons, and variance calculations in alert messages.
3. Ensure all alerts contain actionable recommendations appropriate to the severity level and alert type.
4. Maintain professional communication standards with clear priority indicators and escalation paths.
5. Track and report notification delivery status across all configured channels.
6. Provide comprehensive summaries that include alert counts, delivery metrics, and next steps."""

        configuration_section = f"""
ALERT CONFIGURATION FOR {company_name.upper()}:
- Enabled Channels: {', '.join(enabled_channels)}
- Shortage Threshold: {shortage_threshold} (triggers when shortage index exceeds this value)
- Revenue Decline Threshold: {revenue_decline_threshold:.1%} quarter-over-quarter
- Profit Margin Threshold: {profit_margin_threshold:.1%} reduction
- Cash Flow Monitoring: {cash_flow_periods} consecutive negative periods
- Debt-to-Equity Threshold: {debt_equity_threshold:.1f}

ALERT SEVERITY CRITERIA:
• CRITICAL: Shortage index > 0.8, Revenue decline > 20%, Cash flow negative 3+ periods
• HIGH: Shortage index 0.5-0.8, Revenue decline 10-20%, Cash flow negative 2+ periods
• MEDIUM: Shortage index 0.3-0.5, Profit margin reduction > 15%, Debt-to-equity > threshold
• LOW: Shortage index < 0.3, Minor deviations from benchmarks

NOTIFICATION CHANNEL SELECTION:
• CRITICAL: Email + MQTT + HTTP webhook (immediate escalation)
• HIGH: Email + MQTT (urgent notification)
• MEDIUM: Email only (standard notification)
• LOW: MQTT topic notification (monitoring dashboard)"""

        # Combine all sections into structured instruction
        return f"""{background_section}

PROCESSING STEPS:
{steps_section}

OUTPUT REQUIREMENTS:
{output_instructions_section}

{configuration_section}

Always prioritize critical alerts and ensure timely delivery of important financial information with clear, actionable intelligence."""

    def _build_default_config(self, user_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build default configuration with user overrides and environment variables.

        Creates a comprehensive configuration dictionary by merging default
        alert thresholds and notification settings with environment variables
        and user-provided overrides in order of precedence.

        Args:
            user_config: User-provided configuration overrides

        Returns:
            Complete configuration dictionary with defaults, environment, and user overrides

        Configuration Precedence (highest to lowest):
        1. User-provided configuration (user_config parameter)
        2. Environment variables (ALERT_* prefixed)
        3. Default configuration values

        Raises:
            ValueError: If configuration validation fails
        """
        # Default configuration
        default_config = {
            'shortage_threshold': 0.7,
            'revenue_decline_threshold': 0.10,
            'profit_margin_threshold': 0.15,
            'cash_flow_periods': 2,
            'debt_equity_threshold': 1.2,
            'supplier_dependency_threshold': 0.30,
            'lead_time_increase_threshold': 0.50,
            'email_recipients': ['<EMAIL>'],
            'mqtt_broker': 'localhost:1883',
            'mqtt_topic': '/notification',
            'webhook_url': 'http://localhost:5000/alert',
            'enabled_channels': ['mqtt', 'http'],
            'retry_attempts': 3,
            'retry_delay_seconds': 5
        }

        # Load environment configuration
        env_config = self._load_environment_config()

        # Merge configurations in order of precedence
        merged_config = self._merge_configurations(default_config, env_config, user_config or {})

        # Validate the final configuration
        validation_result = self._validate_configuration(merged_config)

        if not validation_result['is_valid']:
            error_msg = f"Configuration validation failed: {'; '.join(validation_result['errors'])}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # Log warnings if any
        for warning in validation_result['warnings']:
            logger.warning(f"Configuration warning: {warning}")

        # Use sanitized configuration
        final_config = validation_result['sanitized_config']

        logger.info(f"Alert configuration loaded successfully with {len(final_config)} settings")
        logger.debug(f"Final configuration: {final_config}")

        return final_config

    def _build_instruction_old(self) -> str:
        """Build instruction string for BaseAgentWrapper."""
        return f"""You are an Alert Manager Agent specializing in intelligent notification and alert management for {self.company_name}.

Your primary responsibilities:
1. Evaluate alert conditions based on financial analysis and shortage data
2. Determine appropriate alert severity levels and urgency
3. Route notifications through multiple channels (email, MQTT, HTTP webhooks)
4. Manage alert escalation and notification timing
5. Provide comprehensive alert summaries and delivery status

Alert Configuration:
- Shortage Threshold: {self.alert_config.get('shortage_threshold', 0.7)}
- Email Recipients: {', '.join(self.alert_config.get('email_recipients', ['<EMAIL>']))}
- MQTT Broker: {self.alert_config.get('mqtt_broker', 'localhost:1883')}
- Enabled Channels: {', '.join(self.alert_config.get('enabled_channels', ['mqtt', 'http']))}

Alert Evaluation Criteria:
1. Shortage Index Alerts:
   - CRITICAL: Shortage index > 0.8 (immediate notification required)
   - HIGH: Shortage index 0.5-0.8 (notification within 1 hour)
   - MEDIUM: Shortage index 0.3-0.5 (notification within 4 hours)
   - LOW: Shortage index < 0.3 (daily summary notification)

2. Financial Metric Alerts:
   - Revenue decline > {self.alert_config.get('revenue_decline_threshold', 0.10) * 100}% quarter-over-quarter
   - Profit margin reduction > {self.alert_config.get('profit_margin_threshold', 0.15) * 100}%
   - Cash flow negative for {self.alert_config.get('cash_flow_periods', 2)}+ consecutive periods
   - Debt-to-equity ratio > {self.alert_config.get('debt_equity_threshold', 1.2)}

3. Supply Chain Risk Alerts:
   - Critical supplier dependency (single source > {self.alert_config.get('supplier_dependency_threshold', 0.30) * 100}% of supplies)
   - Lead time increases > {self.alert_config.get('lead_time_increase_threshold', 0.50) * 100}% from historical average

Notification Channel Selection:
- CRITICAL: Email + MQTT + HTTP webhook (if configured)
- HIGH: Email + MQTT
- MEDIUM: Email only
- LOW: MQTT topic notification only

Always maintain professional communication standards and ensure all alerts provide actionable intelligence rather than just raw data notifications."""

    async def evaluate_alerts(self, financial_data: Dict[str, Any]) -> List[Alert]:
        """
        Evaluate financial data and generate alerts based on configured thresholds.

        Args:
            financial_data: Financial analysis results from other agents

        Returns:
            List of generated alerts
        """
        alerts = []
        current_time = datetime.now()

        try:
            # Extract data from different sources
            analysis_data = financial_data.get('analysis_data', {})
            shortage_data = financial_data.get('shortage_data', {})

            # Evaluate shortage-based alerts
            shortage_alerts = await self._evaluate_shortage_alerts(shortage_data, current_time)
            alerts.extend(shortage_alerts)

            # Evaluate financial metric alerts
            financial_alerts = await self._evaluate_financial_alerts(analysis_data, current_time)
            alerts.extend(financial_alerts)

            # Update metrics
            self.processing_metrics["alerts_generated"] += len(alerts)
            self.processing_metrics["last_processing_time"] = current_time

            logger.info(f"Generated {len(alerts)} alerts for {self.company_name}")

        except Exception as e:
            logger.error(f"Error evaluating alerts: {e}")
            # Create error alert
            error_alert = Alert(
                id=f"error_{current_time.timestamp()}",
                alert_type=AlertType.OPERATIONAL,
                severity=AlertSeverity.HIGH,
                title="Alert Evaluation Error",
                message=f"Failed to evaluate alerts: {str(e)}",
                data={"error": str(e)},
                threshold_value=0.0,
                actual_value=1.0,
                timestamp=current_time,
                company_name=self.company_name
            )
            alerts.append(error_alert)

        return alerts

    async def _evaluate_shortage_alerts(self, shortage_data: Dict[str, Any], timestamp: datetime) -> List[Alert]:
        """Evaluate shortage-based alerts."""
        alerts = []

        if not shortage_data:
            return alerts

        shortage_index = shortage_data.get('shortage_index', 0.0)
        threshold = self.alert_config.get('shortage_threshold', 0.7)

        if shortage_index > threshold:
            # Determine severity based on shortage level
            if shortage_index >= 0.8:
                severity = AlertSeverity.CRITICAL
            elif shortage_index >= 0.5:
                severity = AlertSeverity.HIGH
            elif shortage_index >= 0.3:
                severity = AlertSeverity.MEDIUM
            else:
                severity = AlertSeverity.LOW

            # Enhanced alert with proper formatting and categorization
            # Use specific shortage description if available, otherwise use generic message
            if 'shortage_description' in shortage_data and shortage_data['shortage_description']:
                base_message = shortage_data['shortage_description']
                if 'action_required' in shortage_data and shortage_data['action_required']:
                    base_message += f" {shortage_data['action_required']}"
            else:
                base_message = "Supply chain disruption detected. Review supplier performance and inventory levels."

            alert_message = self._format_alert_message(
                AlertType.SUPPLY_CHAIN,
                severity,
                "Shortage Index",
                shortage_index,
                threshold,
                base_message
            )

            # Create title with material type if available
            material_type = shortage_data.get('material_type', '')
            if material_type:
                title = f"{material_type} Material Shortage Alert - {self.company_name}"
            else:
                title = f"Supply Chain Shortage Alert - {self.company_name}"

            alert = Alert(
                id=self._generate_alert_id("shortage", timestamp),
                alert_type=AlertType.SUPPLY_CHAIN,
                severity=severity,
                title=title,
                message=alert_message,
                data={**shortage_data, "categorization": self._categorize_alert_by_impact(Alert(
                    id="temp", alert_type=AlertType.SUPPLY_CHAIN, severity=severity,
                    title="", message="", data={}, threshold_value=threshold,
                    actual_value=shortage_index, timestamp=timestamp, company_name=self.company_name
                ))},
                threshold_value=threshold,
                actual_value=shortage_index,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        return alerts


    async def _evaluate_financial_alerts(self, analysis_data: Dict[str, Any], timestamp: datetime) -> List[Alert]:
        """Evaluate financial metric-based alerts with comprehensive threshold monitoring."""
        alerts = []

        if not analysis_data:
            return alerts

        # Revenue decline monitoring
        alerts.extend(await self._check_revenue_thresholds(analysis_data, timestamp))

        # Profit margin monitoring
        alerts.extend(await self._check_profit_margin_thresholds(analysis_data, timestamp))

        # Cash flow monitoring
        alerts.extend(await self._check_cash_flow_thresholds(analysis_data, timestamp))

        # Debt-to-equity monitoring
        alerts.extend(await self._check_debt_equity_thresholds(analysis_data, timestamp))

        # Supply chain risk monitoring
        alerts.extend(await self._check_supply_chain_thresholds(analysis_data, timestamp))

        return alerts

    async def _check_revenue_thresholds(self, analysis_data: Dict[str, Any], timestamp: datetime) -> List[Alert]:
        """Check revenue-related thresholds."""
        alerts = []

        # Quarter-over-quarter revenue decline
        revenue_decline = analysis_data.get('revenue_decline_pct', 0.0)
        revenue_threshold = self.alert_config.get('revenue_decline_threshold', 0.10)

        if revenue_decline > revenue_threshold:
            severity = AlertSeverity.CRITICAL if revenue_decline > 0.20 else AlertSeverity.HIGH

            alert = Alert(
                id=f"revenue_decline_{timestamp.timestamp()}",
                alert_type=AlertType.FINANCIAL,
                severity=severity,
                title=f"Revenue Decline Alert - {self.company_name}",
                message=f"Revenue declined by {revenue_decline:.1%} quarter-over-quarter, exceeding threshold of {revenue_threshold:.1%}. "
                       f"Immediate review of sales strategy and market conditions recommended.",
                data={"revenue_decline": revenue_decline, "analysis": analysis_data, "metric_type": "revenue"},
                threshold_value=revenue_threshold,
                actual_value=revenue_decline,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        # Year-over-year revenue growth monitoring
        yoy_growth = analysis_data.get('revenue_yoy_growth', 0.0)
        if yoy_growth < -0.05:  # Negative growth > 5%
            alert = Alert(
                id=f"revenue_yoy_{timestamp.timestamp()}",
                alert_type=AlertType.FINANCIAL,
                severity=AlertSeverity.MEDIUM,
                title=f"Revenue Growth Concern - {self.company_name}",
                message=f"Year-over-year revenue growth is {yoy_growth:.1%}, indicating potential market challenges.",
                data={"yoy_growth": yoy_growth, "analysis": analysis_data, "metric_type": "revenue_growth"},
                threshold_value=-0.05,
                actual_value=yoy_growth,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        return alerts

    async def _check_profit_margin_thresholds(self, analysis_data: Dict[str, Any], timestamp: datetime) -> List[Alert]:
        """Check profit margin-related thresholds."""
        alerts = []

        # Gross profit margin reduction
        margin_reduction = analysis_data.get('profit_margin_reduction', 0.0)
        margin_threshold = self.alert_config.get('profit_margin_threshold', 0.15)

        if margin_reduction > margin_threshold:
            severity = AlertSeverity.HIGH if margin_reduction > 0.25 else AlertSeverity.MEDIUM

            alert = Alert(
                id=f"profit_margin_{timestamp.timestamp()}",
                alert_type=AlertType.FINANCIAL,
                severity=severity,
                title=f"Profit Margin Alert - {self.company_name}",
                message=f"Profit margin reduced by {margin_reduction:.1%}, exceeding threshold of {margin_threshold:.1%}. "
                       f"Cost management and pricing strategy review recommended.",
                data={"margin_reduction": margin_reduction, "analysis": analysis_data, "metric_type": "profit_margin"},
                threshold_value=margin_threshold,
                actual_value=margin_reduction,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        # Operating margin monitoring (only if data is provided)
        if 'operating_margin' in analysis_data:
            operating_margin = analysis_data['operating_margin']
            if operating_margin < 0.05:  # Operating margin below 5%
                alert = Alert(
                    id=f"operating_margin_{timestamp.timestamp()}",
                    alert_type=AlertType.FINANCIAL,
                    severity=AlertSeverity.HIGH,
                    title=f"Operating Margin Warning - {self.company_name}",
                    message=f"Operating margin is {operating_margin:.1%}, below healthy threshold of 5%. "
                           f"Operational efficiency review required.",
                    data={"operating_margin": operating_margin, "analysis": analysis_data, "metric_type": "operating_margin"},
                    threshold_value=0.05,
                    actual_value=operating_margin,
                    timestamp=timestamp,
                    company_name=self.company_name
                )
                alerts.append(alert)

        return alerts

    async def _check_cash_flow_thresholds(self, analysis_data: Dict[str, Any], timestamp: datetime) -> List[Alert]:
        """Check cash flow-related thresholds."""
        alerts = []

        # Consecutive negative cash flow periods
        negative_periods = analysis_data.get('negative_cash_flow_periods', 0)
        cash_flow_threshold = self.alert_config.get('cash_flow_periods', 2)

        if negative_periods >= cash_flow_threshold:
            severity = AlertSeverity.CRITICAL if negative_periods >= 3 else AlertSeverity.HIGH

            alert = Alert(
                id=f"cash_flow_{timestamp.timestamp()}",
                alert_type=AlertType.FINANCIAL,
                severity=severity,
                title=f"Cash Flow Alert - {self.company_name}",
                message=f"Negative cash flow for {negative_periods} consecutive periods, exceeding threshold of {cash_flow_threshold}. "
                       f"Liquidity management and working capital optimization required.",
                data={"negative_periods": negative_periods, "analysis": analysis_data, "metric_type": "cash_flow"},
                threshold_value=cash_flow_threshold,
                actual_value=negative_periods,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        # Current cash flow ratio
        cash_flow_ratio = analysis_data.get('cash_flow_ratio', 1.0)
        if cash_flow_ratio < 0.8:  # Cash flow ratio below 0.8
            alert = Alert(
                id=f"cash_flow_ratio_{timestamp.timestamp()}",
                alert_type=AlertType.FINANCIAL,
                severity=AlertSeverity.MEDIUM,
                title=f"Cash Flow Ratio Warning - {self.company_name}",
                message=f"Cash flow ratio is {cash_flow_ratio:.2f}, below healthy threshold of 0.8. "
                       f"Monitor cash conversion cycle and payment terms.",
                data={"cash_flow_ratio": cash_flow_ratio, "analysis": analysis_data, "metric_type": "cash_flow_ratio"},
                threshold_value=0.8,
                actual_value=cash_flow_ratio,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        return alerts

    async def _check_debt_equity_thresholds(self, analysis_data: Dict[str, Any], timestamp: datetime) -> List[Alert]:
        """Check debt-to-equity ratio thresholds."""
        alerts = []

        debt_equity_ratio = analysis_data.get('debt_equity_ratio', 0.0)
        debt_threshold = self.alert_config.get('debt_equity_threshold', 1.2)

        if debt_equity_ratio > debt_threshold:
            severity = AlertSeverity.HIGH if debt_equity_ratio > 2.0 else AlertSeverity.MEDIUM

            alert = Alert(
                id=f"debt_equity_{timestamp.timestamp()}",
                alert_type=AlertType.FINANCIAL,
                severity=severity,
                title=f"Debt-to-Equity Alert - {self.company_name}",
                message=f"Debt-to-equity ratio is {debt_equity_ratio:.2f}, exceeding threshold of {debt_threshold:.2f}. "
                       f"Consider debt reduction strategies and capital structure optimization.",
                data={"debt_equity_ratio": debt_equity_ratio, "analysis": analysis_data, "metric_type": "debt_equity"},
                threshold_value=debt_threshold,
                actual_value=debt_equity_ratio,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        # Interest coverage ratio
        interest_coverage = analysis_data.get('interest_coverage_ratio', 10.0)
        if interest_coverage < 2.5:  # Interest coverage below 2.5x
            severity = AlertSeverity.CRITICAL if interest_coverage < 1.5 else AlertSeverity.HIGH

            alert = Alert(
                id=f"interest_coverage_{timestamp.timestamp()}",
                alert_type=AlertType.FINANCIAL,
                severity=severity,
                title=f"Interest Coverage Alert - {self.company_name}",
                message=f"Interest coverage ratio is {interest_coverage:.1f}x, below safe threshold of 2.5x. "
                       f"Debt service capability may be at risk.",
                data={"interest_coverage": interest_coverage, "analysis": analysis_data, "metric_type": "interest_coverage"},
                threshold_value=2.5,
                actual_value=interest_coverage,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        return alerts

    async def _check_supply_chain_thresholds(self, analysis_data: Dict[str, Any], timestamp: datetime) -> List[Alert]:
        """Check supply chain risk thresholds."""
        alerts = []

        # Supplier dependency concentration
        max_supplier_dependency = analysis_data.get('max_supplier_dependency', 0.0)
        supplier_threshold = self.alert_config.get('supplier_dependency_threshold', 0.30)

        if max_supplier_dependency > supplier_threshold:
            severity = AlertSeverity.HIGH if max_supplier_dependency > 0.50 else AlertSeverity.MEDIUM

            alert = Alert(
                id=f"supplier_dependency_{timestamp.timestamp()}",
                alert_type=AlertType.SUPPLY_CHAIN,
                severity=severity,
                title=f"Supplier Dependency Alert - {self.company_name}",
                message=f"Single supplier dependency is {max_supplier_dependency:.1%}, exceeding threshold of {supplier_threshold:.1%}. "
                       f"Supplier diversification strategy recommended.",
                data={"supplier_dependency": max_supplier_dependency, "analysis": analysis_data, "metric_type": "supplier_dependency"},
                threshold_value=supplier_threshold,
                actual_value=max_supplier_dependency,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        # Inventory turnover ratio
        inventory_turnover = analysis_data.get('inventory_turnover', 6.0)
        industry_benchmark = analysis_data.get('industry_inventory_benchmark', 4.0)

        if inventory_turnover < industry_benchmark * 0.8:  # 20% below industry benchmark
            alert = Alert(
                id=f"inventory_turnover_{timestamp.timestamp()}",
                alert_type=AlertType.SUPPLY_CHAIN,
                severity=AlertSeverity.MEDIUM,
                title=f"Inventory Turnover Warning - {self.company_name}",
                message=f"Inventory turnover is {inventory_turnover:.1f}x, significantly below industry benchmark of {industry_benchmark:.1f}x. "
                       f"Inventory management optimization needed.",
                data={"inventory_turnover": inventory_turnover, "benchmark": industry_benchmark, "analysis": analysis_data, "metric_type": "inventory_turnover"},
                threshold_value=industry_benchmark * 0.8,
                actual_value=inventory_turnover,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        # Lead time increases
        lead_time_increase = analysis_data.get('lead_time_increase_pct', 0.0)
        lead_time_threshold = self.alert_config.get('lead_time_increase_threshold', 0.50)

        if lead_time_increase > lead_time_threshold:
            severity = AlertSeverity.HIGH if lead_time_increase > 1.0 else AlertSeverity.MEDIUM

            alert = Alert(
                id=f"lead_time_{timestamp.timestamp()}",
                alert_type=AlertType.SUPPLY_CHAIN,
                severity=severity,
                title=f"Lead Time Alert - {self.company_name}",
                message=f"Lead times increased by {lead_time_increase:.1%}, exceeding threshold of {lead_time_threshold:.1%}. "
                       f"Supply chain disruption mitigation required.",
                data={"lead_time_increase": lead_time_increase, "analysis": analysis_data, "metric_type": "lead_time"},
                threshold_value=lead_time_threshold,
                actual_value=lead_time_increase,
                timestamp=timestamp,
                company_name=self.company_name
            )
            alerts.append(alert)

        return alerts

    def _generate_alert_id(self, alert_type: str, timestamp: datetime) -> str:
        """Generate unique alert ID with type and timestamp."""
        return f"{alert_type}_{self.company_name.lower().replace(' ', '_')}_{int(timestamp.timestamp())}"

    def _format_alert_message(self, alert_type: AlertType, severity: AlertSeverity,
                             metric_name: str, actual_value: float, threshold_value: float,
                             additional_context: str = "") -> str:
        """Format alert message with consistent structure and actionable content."""

        # Severity indicator
        severity_indicator = {
            AlertSeverity.CRITICAL: "🚨 CRITICAL",
            AlertSeverity.HIGH: "⚠️ HIGH PRIORITY",
            AlertSeverity.MEDIUM: "⚡ MEDIUM PRIORITY",
            AlertSeverity.LOW: "ℹ️ LOW PRIORITY"
        }

        # Alert type context
        type_context = {
            AlertType.FINANCIAL: "Financial Performance",
            AlertType.SUPPLY_CHAIN: "Supply Chain Risk",
            AlertType.OPERATIONAL: "Operational Issue"
        }

        # Base message structure
        message = f"{severity_indicator[severity]} - {type_context[alert_type]} Alert\n\n"
        message += f"Company: {self.company_name}\n"
        message += f"Metric: {metric_name}\n"
        message += f"Current Value: {self._format_metric_value(metric_name, actual_value)}\n"
        message += f"Threshold: {self._format_metric_value(metric_name, threshold_value)}\n"
        message += f"Variance: {self._calculate_variance_description(actual_value, threshold_value, metric_name)}\n\n"

        # Add context and recommendations
        if additional_context:
            message += f"Context: {additional_context}\n\n"

        # Add severity-specific recommendations
        message += self._get_severity_recommendations(severity, alert_type)

        return message

    def _format_metric_value(self, metric_name: str, value: float) -> str:
        """Format metric values based on type."""
        percentage_metrics = [
            'revenue_decline', 'profit_margin_reduction', 'yoy_growth',
            'operating_margin', 'supplier_dependency', 'lead_time_increase'
        ]

        ratio_metrics = [
            'debt_equity_ratio', 'cash_flow_ratio', 'interest_coverage'
        ]

        if any(pm in metric_name.lower() for pm in percentage_metrics):
            return f"{value:.1%}"
        elif any(rm in metric_name.lower() for rm in ratio_metrics):
            return f"{value:.2f}x" if 'coverage' in metric_name.lower() else f"{value:.2f}"
        elif 'periods' in metric_name.lower():
            return f"{int(value)} periods"
        elif 'turnover' in metric_name.lower():
            return f"{value:.1f}x"
        else:
            return f"{value:.3f}"

    def _calculate_variance_description(self, actual: float, threshold: float, metric_name: str) -> str:
        """Calculate and describe variance from threshold."""
        if 'decline' in metric_name.lower() or 'increase' in metric_name.lower():
            # For metrics where higher is worse
            if actual > threshold:
                variance = ((actual - threshold) / threshold) * 100
                return f"{variance:.1f}% above threshold"
            else:
                return "Within acceptable range"
        else:
            # For metrics where lower is worse
            if actual < threshold:
                variance = ((threshold - actual) / threshold) * 100
                return f"{variance:.1f}% below threshold"
            else:
                return "Within acceptable range"

    def _get_severity_recommendations(self, severity: AlertSeverity, alert_type: AlertType) -> str:
        """Get recommendations based on severity and alert type."""
        recommendations = {
            AlertSeverity.CRITICAL: {
                AlertType.FINANCIAL: "IMMEDIATE ACTION REQUIRED:\n• Convene emergency financial review meeting\n• Assess liquidity position and credit facilities\n• Consider immediate cost reduction measures\n• Notify board and key stakeholders",
                AlertType.SUPPLY_CHAIN: "IMMEDIATE ACTION REQUIRED:\n• Activate supply chain contingency plans\n• Contact alternative suppliers immediately\n• Assess inventory levels and production impact\n• Implement emergency procurement procedures",
                AlertType.OPERATIONAL: "IMMEDIATE ACTION REQUIRED:\n• Escalate to senior management\n• Implement emergency response procedures\n• Assess system impact and recovery options\n• Notify relevant stakeholders"
            },
            AlertSeverity.HIGH: {
                AlertType.FINANCIAL: "ACTION REQUIRED WITHIN 1 HOUR:\n• Schedule urgent financial review\n• Analyze root causes and trends\n• Prepare corrective action plan\n• Monitor key metrics closely",
                AlertType.SUPPLY_CHAIN: "ACTION REQUIRED WITHIN 1 HOUR:\n• Review supplier performance and contracts\n• Assess supply chain alternatives\n• Implement risk mitigation measures\n• Update procurement strategies",
                AlertType.OPERATIONAL: "ACTION REQUIRED WITHIN 1 HOUR:\n• Investigate operational issues\n• Implement corrective measures\n• Monitor system performance\n• Update operational procedures"
            },
            AlertSeverity.MEDIUM: {
                AlertType.FINANCIAL: "ACTION REQUIRED WITHIN 4 HOURS:\n• Conduct detailed financial analysis\n• Identify improvement opportunities\n• Develop action plan with timeline\n• Schedule regular monitoring",
                AlertType.SUPPLY_CHAIN: "ACTION REQUIRED WITHIN 4 HOURS:\n• Review supply chain performance\n• Optimize inventory management\n• Strengthen supplier relationships\n• Implement preventive measures",
                AlertType.OPERATIONAL: "ACTION REQUIRED WITHIN 4 HOURS:\n• Analyze operational metrics\n• Optimize processes and procedures\n• Implement efficiency improvements\n• Schedule regular reviews"
            },
            AlertSeverity.LOW: {
                AlertType.FINANCIAL: "DAILY MONITORING:\n• Include in daily financial dashboard\n• Track trends and patterns\n• Consider preventive measures\n• Schedule periodic review",
                AlertType.SUPPLY_CHAIN: "DAILY MONITORING:\n• Monitor supply chain metrics\n• Track supplier performance\n• Review inventory levels\n• Update risk assessments",
                AlertType.OPERATIONAL: "DAILY MONITORING:\n• Include in operational reports\n• Monitor performance trends\n• Consider process improvements\n• Schedule routine maintenance"
            }
        }

        return recommendations.get(severity, {}).get(alert_type, "Monitor situation and take appropriate action.")

    def _prioritize_alerts_by_business_impact(self, alerts: List[Alert]) -> List[Alert]:
        """Prioritize alerts based on comprehensive business impact assessment."""

        def calculate_priority_score(alert: Alert) -> float:
            """Calculate priority score for alert ranking."""
            base_score = {
                AlertSeverity.CRITICAL: 100,
                AlertSeverity.HIGH: 75,
                AlertSeverity.MEDIUM: 50,
                AlertSeverity.LOW: 25
            }[alert.severity]

            # Type-based multipliers
            type_multiplier = {
                AlertType.FINANCIAL: 1.2,      # Financial alerts get higher priority
                AlertType.SUPPLY_CHAIN: 1.1,   # Supply chain alerts are important
                AlertType.OPERATIONAL: 1.0     # Operational alerts baseline
            }[alert.alert_type]

            # Variance-based adjustment
            variance_multiplier = self._calculate_variance_multiplier(alert)

            # Time-based urgency (newer alerts get slight priority boost)
            time_multiplier = self._calculate_time_urgency_multiplier(alert)

            return base_score * type_multiplier * variance_multiplier * time_multiplier

        # Sort by priority score (highest first)
        return sorted(alerts, key=calculate_priority_score, reverse=True)

    def _calculate_variance_multiplier(self, alert: Alert) -> float:
        """Calculate multiplier based on how far the metric is from threshold."""
        if alert.threshold_value == 0:
            return 1.0

        variance_ratio = abs(alert.actual_value - alert.threshold_value) / alert.threshold_value

        # Higher variance = higher priority (capped at 1.5x)
        return min(1.0 + (variance_ratio * 0.5), 1.5)

    def _calculate_time_urgency_multiplier(self, alert: Alert) -> float:
        """Calculate time-based urgency multiplier."""
        time_diff = (datetime.now() - alert.timestamp).total_seconds()

        # Newer alerts get slight priority boost (within 1 hour)
        if time_diff < 3600:  # 1 hour
            return 1.1
        elif time_diff < 7200:  # 2 hours
            return 1.05
        else:
            return 1.0

    def _apply_business_rules_prioritization(self, alerts: List[Alert]) -> List[Alert]:
        """Apply business-specific rules for alert prioritization."""
        # Separate alerts by type for business rule application
        financial_alerts = [a for a in alerts if a.alert_type == AlertType.FINANCIAL]
        supply_chain_alerts = [a for a in alerts if a.alert_type == AlertType.SUPPLY_CHAIN]
        operational_alerts = [a for a in alerts if a.alert_type == AlertType.OPERATIONAL]

        # Apply financial alert business rules
        financial_alerts = self._apply_financial_business_rules(financial_alerts)

        # Apply supply chain alert business rules
        supply_chain_alerts = self._apply_supply_chain_business_rules(supply_chain_alerts)

        # Apply operational alert business rules
        operational_alerts = self._apply_operational_business_rules(operational_alerts)

        # Combine and re-prioritize
        all_alerts = financial_alerts + supply_chain_alerts + operational_alerts
        return self._prioritize_alerts_by_business_impact(all_alerts)

    def _apply_financial_business_rules(self, alerts: List[Alert]) -> List[Alert]:
        """Apply business rules specific to financial alerts."""
        for alert in alerts:
            # Cash flow alerts get elevated priority during certain periods
            if 'cash_flow' in alert.id and alert.severity == AlertSeverity.HIGH:
                # Check if it's end of quarter (elevated priority)
                current_month = datetime.now().month
                if current_month in [3, 6, 9, 12]:  # Quarter ends
                    alert.severity = AlertSeverity.CRITICAL
                    alert.metadata['priority_elevation'] = 'End of quarter cash flow concern'

            # Revenue decline alerts get elevated if consecutive
            if 'revenue_decline' in alert.id:
                # Check for consecutive revenue declines (would need historical data)
                alert.metadata['consecutive_decline_check'] = 'Requires historical analysis'

        return alerts

    def _apply_supply_chain_business_rules(self, alerts: List[Alert]) -> List[Alert]:
        """Apply business rules specific to supply chain alerts."""
        for alert in alerts:
            # Supplier dependency alerts during peak seasons
            if 'supplier_dependency' in alert.id:
                # Check if it's peak season (Q4 for many businesses)
                current_month = datetime.now().month
                if current_month in [10, 11, 12]:  # Q4 peak season
                    if alert.severity == AlertSeverity.MEDIUM:
                        alert.severity = AlertSeverity.HIGH
                        alert.metadata['priority_elevation'] = 'Peak season supplier risk'

            # Lead time alerts get elevated during supply chain disruptions
            if 'lead_time' in alert.id and alert.actual_value > 0.75:  # >75% increase
                alert.severity = AlertSeverity.HIGH
                alert.metadata['priority_elevation'] = 'Severe lead time disruption'

        return alerts

    def _apply_operational_business_rules(self, alerts: List[Alert]) -> List[Alert]:
        """Apply business rules specific to operational alerts."""
        for alert in alerts:
            # System alerts during business hours get elevated priority
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 17:  # Business hours
                if alert.severity == AlertSeverity.MEDIUM:
                    alert.severity = AlertSeverity.HIGH
                    alert.metadata['priority_elevation'] = 'Business hours operational impact'

        return alerts

    def _determine_notification_channels_by_priority(self, alert: Alert) -> List[NotificationChannel]:
        """Determine notification channels based on alert priority and business rules."""
        base_channels = self._get_channels_for_severity(alert.severity)

        # Convert string channels to NotificationChannel enums
        channels = []
        for channel_name in base_channels:
            try:
                if channel_name in self.alert_config.get('enabled_channels', ['mqtt', 'http']):
                    channels.append(NotificationChannel(channel_name))
            except ValueError:
                logger.warning(f"Invalid channel name: {channel_name}")

        # Business rule adjustments
        if alert.alert_type == AlertType.FINANCIAL and alert.severity == AlertSeverity.CRITICAL:
            # Ensure all channels are used for critical financial alerts
            all_channels = [NotificationChannel.EMAIL, NotificationChannel.MQTT, NotificationChannel.HTTP]
            for channel in all_channels:
                if channel not in channels and channel.value in self.alert_config.get('enabled_channels', []):
                    channels.append(channel)

        return channels

    def _generate_escalation_timeline(self, alert: Alert) -> Dict[str, str]:
        """Generate escalation timeline based on alert priority."""
        timelines = {
            AlertSeverity.CRITICAL: {
                "immediate": "0-15 minutes: Notify on-call team and senior management",
                "short_term": "15-30 minutes: Escalate to C-level executives",
                "medium_term": "30-60 minutes: Board notification if unresolved",
                "long_term": "1+ hours: External stakeholder communication"
            },
            AlertSeverity.HIGH: {
                "immediate": "0-30 minutes: Notify department heads",
                "short_term": "30-60 minutes: Senior management notification",
                "medium_term": "1-2 hours: Cross-functional team assembly",
                "long_term": "2+ hours: Stakeholder updates"
            },
            AlertSeverity.MEDIUM: {
                "immediate": "0-1 hour: Team lead notification",
                "short_term": "1-4 hours: Department manager review",
                "medium_term": "4-8 hours: Action plan development",
                "long_term": "8+ hours: Progress reporting"
            },
            AlertSeverity.LOW: {
                "immediate": "0-4 hours: Include in daily reports",
                "short_term": "4-24 hours: Team review",
                "medium_term": "1-3 days: Trend analysis",
                "long_term": "3+ days: Preventive measures"
            }
        }

        return timelines.get(alert.severity, timelines[AlertSeverity.LOW])

    def _categorize_alert_by_impact(self, alert: Alert) -> Dict[str, str]:
        """Categorize alert by business impact and urgency."""
        impact_categories = {
            "business_impact": self._assess_business_impact(alert),
            "urgency_level": self._assess_urgency_level(alert),
            "stakeholder_groups": self._identify_stakeholder_groups(alert),
            "escalation_path": self._determine_escalation_path(alert)
        }

        return impact_categories

    def _assess_business_impact(self, alert: Alert) -> str:
        """Assess business impact level."""
        if alert.severity == AlertSeverity.CRITICAL:
            return "High - Immediate business risk"
        elif alert.severity == AlertSeverity.HIGH:
            return "Medium-High - Significant business concern"
        elif alert.severity == AlertSeverity.MEDIUM:
            return "Medium - Moderate business impact"
        else:
            return "Low - Minimal immediate impact"

    def _assess_urgency_level(self, alert: Alert) -> str:
        """Assess urgency level for response."""
        urgency_map = {
            AlertSeverity.CRITICAL: "Immediate (0-15 minutes)",
            AlertSeverity.HIGH: "Urgent (1 hour)",
            AlertSeverity.MEDIUM: "Moderate (4 hours)",
            AlertSeverity.LOW: "Low (24 hours)"
        }
        return urgency_map.get(alert.severity, "Standard")

    def _identify_stakeholder_groups(self, alert: Alert) -> str:
        """Identify relevant stakeholder groups."""
        if alert.alert_type == AlertType.FINANCIAL:
            if alert.severity in [AlertSeverity.CRITICAL, AlertSeverity.HIGH]:
                return "CFO, CEO, Board, Finance Team, Investors"
            else:
                return "CFO, Finance Team, Department Heads"
        elif alert.alert_type == AlertType.SUPPLY_CHAIN:
            if alert.severity in [AlertSeverity.CRITICAL, AlertSeverity.HIGH]:
                return "COO, Supply Chain Director, Procurement Team, Production Manager"
            else:
                return "Supply Chain Team, Procurement Team"
        else:  # OPERATIONAL
            return "Operations Team, IT Team, Department Managers"

    def _determine_escalation_path(self, alert: Alert) -> str:
        """Determine escalation path based on alert characteristics."""
        if alert.severity == AlertSeverity.CRITICAL:
            return "Immediate escalation to C-level executives and board notification"
        elif alert.severity == AlertSeverity.HIGH:
            return "Escalate to department heads and senior management"
        elif alert.severity == AlertSeverity.MEDIUM:
            return "Notify department managers and relevant teams"
        else:
            return "Standard reporting through regular channels"

    async def process_alerts(self, alerts: List[Alert]) -> AlertProcessingResult:
        """
        Process a batch of alerts including prioritization and notification delivery.

        Args:
            alerts: List of alerts to process

        Returns:
            Processing result with delivery status and errors
        """
        start_time = datetime.now()
        notifications_sent = []
        errors = []

        try:
            # Store alerts
            for alert in alerts:
                self.active_alerts[alert.id] = alert

            # Apply business rules prioritization and comprehensive sorting
            prioritized_alerts = self._apply_business_rules_prioritization(alerts)
            sorted_alerts = self._prioritize_alerts_by_business_impact(prioritized_alerts)

            # Use batch processing for better performance and error handling
            batch_result = await self._batch_process_alerts_async(sorted_alerts)
            notifications_sent.extend(batch_result.notifications_sent)
            errors.extend(batch_result.errors)

            # Update metrics
            self.processing_metrics["notifications_sent"] += len([n for n in notifications_sent if n.status == "success"])
            self.processing_metrics["notifications_failed"] += len([n for n in notifications_sent if n.status == "failed"])

            # Generate summary
            summary = self._generate_processing_summary(alerts, notifications_sent, errors)

            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)

            return AlertProcessingResult(
                processed_alerts=alerts,
                notifications_sent=notifications_sent,
                errors=errors,
                summary=summary,
                processing_time_ms=processing_time
            )

        except Exception as e:
            error_msg = f"Critical error in alert processing: {str(e)}"
            errors.append(error_msg)
            logger.error(error_msg)

            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)

            return AlertProcessingResult(
                processed_alerts=alerts,
                notifications_sent=notifications_sent,
                errors=errors,
                summary=f"Alert processing failed: {error_msg}",
                processing_time_ms=processing_time
            )

    def _generate_processing_summary(self, alerts: List[Alert], notifications: List[NotificationDelivery], errors: List[str]) -> str:
        """Generate a summary of alert processing results."""
        total_alerts = len(alerts)
        successful_notifications = len([n for n in notifications if n.status == "success"])
        failed_notifications = len([n for n in notifications if n.status == "failed"])

        severity_counts = {}
        for alert in alerts:
            severity_counts[alert.severity.value] = severity_counts.get(alert.severity.value, 0) + 1

        summary = f"Processed {total_alerts} alerts for {self.company_name}:\n"
        summary += f"- Notifications sent: {successful_notifications}\n"
        summary += f"- Notifications failed: {failed_notifications}\n"
        summary += f"- Errors encountered: {len(errors)}\n"

        if severity_counts:
            summary += "Alert severity breakdown:\n"
            for severity, count in severity_counts.items():
                summary += f"  - {severity.upper()}: {count}\n"

        return summary

    async def process_financial_analysis(self, input_data: AlertManagementInputSchema) -> AlertManagementOutputSchema:
        """
        Main processing method compatible with BaseAgentWrapper pattern.

        Args:
            input_data: Structured input containing financial analysis data

        Returns:
            Structured output with alert processing results
        """
        try:
            # Parse and enrich input data
            analysis_data = self._parse_json_string(input_data.analysis_data)
            shortage_data = self._parse_json_string(input_data.shortage_data)

            # Enrich data with additional context
            enriched_analysis = self._enrich_financial_data_with_context(analysis_data)
            enriched_shortage = self._enrich_financial_data_with_context(shortage_data)

            # Validate data quality
            analysis_quality = self._validate_financial_data_quality(enriched_analysis)
            shortage_quality = self._validate_financial_data_quality(enriched_shortage)

            # Log data quality assessment
            if not analysis_quality['is_valid']:
                logger.warning(f"Analysis data quality issues: {analysis_quality['issues']}")
            if not shortage_quality['is_valid']:
                logger.warning(f"Shortage data quality issues: {shortage_quality['issues']}")

            financial_data = {
                'analysis_data': enriched_analysis,
                'shortage_data': enriched_shortage,
                'data_quality': {
                    'analysis_quality': analysis_quality,
                    'shortage_quality': shortage_quality
                }
            }

            # Evaluate alerts
            alerts = await self.evaluate_alerts(financial_data)

            # Process alerts if any were generated
            if alerts:
                processing_result = await self.process_alerts(alerts)

                return AlertManagementOutputSchema(
                    company_name=input_data.company_name,
                    alerts_sent=[f"{alert.title} via {', '.join(self._get_channels_for_severity(alert.severity))}"
                               for alert in processing_result.processed_alerts],
                    notification_results={
                        "successful": len([n for n in processing_result.notifications_sent if n.status == "success"]),
                        "failed": len([n for n in processing_result.notifications_sent if n.status == "failed"]),
                        "channels_used": list(set([n.channel.value for n in processing_result.notifications_sent]))
                    },
                    alert_summary=processing_result.summary,
                    response=f"Alert processing completed for {input_data.company_name}. {processing_result.summary}"
                )
            else:
                return AlertManagementOutputSchema(
                    company_name=input_data.company_name,
                    alerts_sent=[],
                    notification_results={"message": "No alerts generated - all metrics within normal thresholds"},
                    alert_summary=f"No alerts required for {input_data.company_name} - all financial and supply chain metrics are within acceptable ranges.",
                    response=f"Alert evaluation completed for {input_data.company_name}. No immediate alerts required."
                )

        except Exception as e:
            error_msg = f"Error in financial analysis processing: {str(e)}"
            logger.error(error_msg)

            return AlertManagementOutputSchema(
                company_name=input_data.company_name,
                alerts_sent=[],
                notification_results={"error": error_msg},
                alert_summary=f"Alert processing failed: {error_msg}",
                response=f"Alert processing failed for {input_data.company_name}: {error_msg}"
            )

    def _parse_json_string(self, json_str: Optional[str]) -> Dict[str, Any]:
        """Parse JSON string safely with enhanced data extraction."""
        if not json_str:
            return {}

        try:
            # If already a dict, return as-is
            if isinstance(json_str, dict):
                return json_str

            # Try to parse as JSON
            if isinstance(json_str, str):
                return json.loads(json_str)

            return {}

        except (json.JSONDecodeError, TypeError):
            # If JSON parsing fails, try to extract data from text
            return self._extract_data_from_text(json_str)

    def _extract_data_from_text(self, text: str) -> Dict[str, Any]:
        """Extract financial data from text when JSON parsing fails."""
        if not isinstance(text, str):
            return {}

        extracted_data = {}

        # Extract common financial metrics using regex patterns
        patterns = {
            'revenue_decline_pct': r'revenue.*decline.*?(\d+\.?\d*)%',
            'profit_margin_reduction': r'profit.*margin.*reduction.*?(\d+\.?\d*)%',
            'shortage_index': r'shortage.*index.*?(\d+\.?\d*)',
            'debt_equity_ratio': r'debt.*equity.*ratio.*?(\d+\.?\d*)',
            'cash_flow_ratio': r'cash.*flow.*ratio.*?(\d+\.?\d*)',
            'operating_margin': r'operating.*margin.*?(\d+\.?\d*)%',
            'inventory_turnover': r'inventory.*turnover.*?(\d+\.?\d*)',
            'supplier_dependency': r'supplier.*dependency.*?(\d+\.?\d*)%',
            'lead_time_increase_pct': r'lead.*time.*increase.*?(\d+\.?\d*)%'
        }

        text_lower = text.lower()

        for key, pattern in patterns.items():
            import re
            match = re.search(pattern, text_lower)
            if match:
                try:
                    value = float(match.group(1))
                    # Convert percentages to decimals
                    if '%' in pattern and key != 'shortage_index':
                        value = value / 100.0
                    extracted_data[key] = value
                except (ValueError, IndexError):
                    continue

        # Extract shortage-specific data
        if 'shortage' in text_lower:
            # Look for shortage index patterns
            shortage_patterns = [
                r'shortage.*index.*?(\d+\.?\d*)',
                r'index.*?(\d+\.?\d*)',
                r'shortage.*?(\d+\.?\d*)'
            ]

            for pattern in shortage_patterns:
                match = re.search(pattern, text_lower)
                if match:
                    try:
                        extracted_data['shortage_index'] = float(match.group(1))
                        break
                    except (ValueError, IndexError):
                        continue

        # Extract financial analysis data
        if any(term in text_lower for term in ['revenue', 'profit', 'earnings', 'financial']):
            # Look for revenue decline
            revenue_patterns = [
                r'revenue.*declined?.*?(\d+\.?\d*)%',
                r'revenue.*down.*?(\d+\.?\d*)%',
                r'revenue.*decrease.*?(\d+\.?\d*)%'
            ]

            for pattern in revenue_patterns:
                match = re.search(pattern, text_lower)
                if match:
                    try:
                        extracted_data['revenue_decline_pct'] = float(match.group(1)) / 100.0
                        break
                    except (ValueError, IndexError):
                        continue

        # Add metadata about extraction
        if extracted_data:
            extracted_data['_extraction_method'] = 'text_parsing'
            extracted_data['_source_text_length'] = len(text)

        return extracted_data

    def _enrich_financial_data_with_context(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich financial data with additional context for better alert evaluation."""
        enriched_data = financial_data.copy()

        # Add derived metrics
        if 'revenue_decline_pct' in enriched_data:
            decline = enriched_data['revenue_decline_pct']
            if decline > 0.15:  # >15% decline
                enriched_data['revenue_risk_level'] = 'high'
            elif decline > 0.05:  # >5% decline
                enriched_data['revenue_risk_level'] = 'medium'
            else:
                enriched_data['revenue_risk_level'] = 'low'

        # Add shortage risk assessment
        if 'shortage_index' in enriched_data:
            index = enriched_data['shortage_index']
            if index > 0.8:
                enriched_data['shortage_risk_level'] = 'critical'
            elif index > 0.5:
                enriched_data['shortage_risk_level'] = 'high'
            elif index > 0.3:
                enriched_data['shortage_risk_level'] = 'medium'
            else:
                enriched_data['shortage_risk_level'] = 'low'

        # Add financial health indicators
        financial_health_score = 100

        if 'revenue_decline_pct' in enriched_data:
            financial_health_score -= enriched_data['revenue_decline_pct'] * 100

        if 'profit_margin_reduction' in enriched_data:
            financial_health_score -= enriched_data['profit_margin_reduction'] * 50

        if 'debt_equity_ratio' in enriched_data:
            if enriched_data['debt_equity_ratio'] > 1.0:
                financial_health_score -= (enriched_data['debt_equity_ratio'] - 1.0) * 20

        enriched_data['financial_health_score'] = max(0, financial_health_score)

        # Add timestamp for data freshness
        enriched_data['_enrichment_timestamp'] = datetime.now().isoformat()

        return enriched_data

    def _validate_financial_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and assess the quality of financial data."""
        quality_assessment = {
            'is_valid': True,
            'completeness_score': 0.0,
            'reliability_score': 0.0,
            'issues': [],
            'recommendations': []
        }

        # Check for required fields
        required_fields = ['shortage_index', 'revenue_decline_pct', 'profit_margin_reduction']
        present_fields = [field for field in required_fields if field in data and data[field] is not None]

        quality_assessment['completeness_score'] = len(present_fields) / len(required_fields)

        # Check data ranges and validity
        range_checks = {
            'shortage_index': (0.0, 1.0),
            'revenue_decline_pct': (0.0, 1.0),
            'profit_margin_reduction': (0.0, 1.0),
            'debt_equity_ratio': (0.0, 10.0),
            'cash_flow_ratio': (0.0, 5.0)
        }

        valid_values = 0
        total_values = 0

        for field, (min_val, max_val) in range_checks.items():
            if field in data and data[field] is not None:
                total_values += 1
                value = data[field]
                if isinstance(value, (int, float)) and min_val <= value <= max_val:
                    valid_values += 1
                else:
                    quality_assessment['issues'].append(f"{field} value {value} is outside expected range [{min_val}, {max_val}]")

        if total_values > 0:
            quality_assessment['reliability_score'] = valid_values / total_values

        # Overall validity
        if quality_assessment['completeness_score'] < 0.3:
            quality_assessment['is_valid'] = False
            quality_assessment['recommendations'].append("Insufficient data for reliable alert evaluation")

        if quality_assessment['reliability_score'] < 0.8:
            quality_assessment['recommendations'].append("Some data values appear unreliable - verify data sources")

        return quality_assessment

    def _validate_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate alert configuration with comprehensive checks.

        Args:
            config: Configuration dictionary to validate

        Returns:
            Validation result with status, errors, and warnings
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'sanitized_config': config.copy()
        }

        # Validate threshold values
        threshold_validations = {
            'shortage_threshold': (0.0, 1.0, 'Shortage threshold must be between 0.0 and 1.0'),
            'revenue_decline_threshold': (0.0, 1.0, 'Revenue decline threshold must be between 0.0 and 1.0'),
            'profit_margin_threshold': (0.0, 1.0, 'Profit margin threshold must be between 0.0 and 1.0'),
            'debt_equity_threshold': (0.1, 10.0, 'Debt-to-equity threshold must be between 0.1 and 10.0'),
            'supplier_dependency_threshold': (0.0, 1.0, 'Supplier dependency threshold must be between 0.0 and 1.0'),
            'lead_time_increase_threshold': (0.0, 5.0, 'Lead time increase threshold must be between 0.0 and 5.0')
        }

        for key, (min_val, max_val, error_msg) in threshold_validations.items():
            if key in config:
                value = config[key]
                if not isinstance(value, (int, float)):
                    validation_result['errors'].append(f"{key} must be a number")
                    validation_result['is_valid'] = False
                elif not (min_val <= value <= max_val):
                    validation_result['errors'].append(error_msg)
                    validation_result['is_valid'] = False

        # Validate integer values
        integer_validations = {
            'cash_flow_periods': (1, 12, 'Cash flow periods must be between 1 and 12'),
            'retry_attempts': (1, 10, 'Retry attempts must be between 1 and 10'),
            'retry_delay_seconds': (1, 300, 'Retry delay must be between 1 and 300 seconds')
        }

        for key, (min_val, max_val, error_msg) in integer_validations.items():
            if key in config:
                value = config[key]
                if not isinstance(value, int):
                    validation_result['errors'].append(f"{key} must be an integer")
                    validation_result['is_valid'] = False
                elif not (min_val <= value <= max_val):
                    validation_result['errors'].append(error_msg)
                    validation_result['is_valid'] = False

        # Validate email recipients
        if 'email_recipients' in config:
            recipients = config['email_recipients']
            if not isinstance(recipients, list):
                validation_result['errors'].append("email_recipients must be a list")
                validation_result['is_valid'] = False
            else:
                valid_recipients = []
                for recipient in recipients:
                    if self._validate_email(recipient):
                        valid_recipients.append(recipient)
                    else:
                        validation_result['warnings'].append(f"Invalid email format: {recipient}")

                if not valid_recipients:
                    validation_result['errors'].append("At least one valid email recipient is required")
                    validation_result['is_valid'] = False
                else:
                    validation_result['sanitized_config']['email_recipients'] = valid_recipients

        # Validate MQTT configuration
        if 'mqtt_broker' in config:
            broker = config['mqtt_broker']
            if not self._validate_mqtt_broker(broker):
                validation_result['errors'].append(f"Invalid MQTT broker format: {broker}")
                validation_result['is_valid'] = False

        # Validate webhook URL
        if 'webhook_url' in config and config['webhook_url']:
            url = config['webhook_url']
            if not self._validate_url(url):
                validation_result['errors'].append(f"Invalid webhook URL format: {url}")
                validation_result['is_valid'] = False

        # Validate enabled channels
        if 'enabled_channels' in config:
            channels = config['enabled_channels']
            if not isinstance(channels, list):
                validation_result['errors'].append("enabled_channels must be a list")
                validation_result['is_valid'] = False
            else:
                valid_channels = []
                for channel in channels:
                    if channel in ['email', 'mqtt', 'http']:
                        valid_channels.append(channel)
                    else:
                        validation_result['warnings'].append(f"Unknown notification channel: {channel}")

                if not valid_channels:
                    validation_result['errors'].append("At least one valid notification channel is required")
                    validation_result['is_valid'] = False
                else:
                    validation_result['sanitized_config']['enabled_channels'] = valid_channels

        return validation_result

    def _validate_email(self, email: str) -> bool:
        """Validate email address format."""
        if not isinstance(email, str):
            return False

        # Basic email validation pattern
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    def _validate_mqtt_broker(self, broker: str) -> bool:
        """Validate MQTT broker format (host:port)."""
        if not isinstance(broker, str):
            return False

        # Pattern for host:port format
        pattern = r'^[a-zA-Z0-9.-]+:\d+$'
        return bool(re.match(pattern, broker))

    def _validate_url(self, url: str) -> bool:
        """Validate URL format."""
        if not isinstance(url, str):
            return False

        # Basic URL validation pattern
        pattern = r'^https?://[a-zA-Z0-9.-]+(?:\:[0-9]+)?(?:/.*)?$'
        return bool(re.match(pattern, url))

    def _load_environment_config(self) -> Dict[str, Any]:
        """
        Load configuration from environment variables.

        Returns:
            Configuration dictionary with environment variable overrides
        """
        env_config = {}

        # Environment variable mappings
        env_mappings = {
            'ALERT_SHORTAGE_THRESHOLD': ('shortage_threshold', float),
            'ALERT_REVENUE_DECLINE_THRESHOLD': ('revenue_decline_threshold', float),
            'ALERT_PROFIT_MARGIN_THRESHOLD': ('profit_margin_threshold', float),
            'ALERT_DEBT_EQUITY_THRESHOLD': ('debt_equity_threshold', float),
            'ALERT_CASH_FLOW_PERIODS': ('cash_flow_periods', int),
            'ALERT_EMAIL_RECIPIENTS': ('email_recipients', lambda x: x.split(',')),
            'ALERT_MQTT_BROKER': ('mqtt_broker', str),
            'ALERT_MQTT_TOPIC': ('mqtt_topic', str),
            'ALERT_WEBHOOK_URL': ('webhook_url', str),
            'ALERT_ENABLED_CHANNELS': ('enabled_channels', lambda x: x.split(',')),
            'ALERT_RETRY_ATTEMPTS': ('retry_attempts', int),
            'ALERT_RETRY_DELAY_SECONDS': ('retry_delay_seconds', int)
        }

        for env_var, (config_key, converter) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    env_config[config_key] = converter(value)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Invalid environment variable {env_var}={value}: {e}")

        return env_config

    def _merge_configurations(self, *configs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge multiple configuration dictionaries with precedence.

        Args:
            *configs: Configuration dictionaries in order of precedence (later overrides earlier)

        Returns:
            Merged configuration dictionary
        """
        merged = {}

        for config in configs:
            if config:
                merged.update(config)

        return merged

    def _setup_error_recovery_strategies(self) -> None:
        """
        Setup error recovery and fallback strategies.

        Initializes error tracking and recovery configuration including:
        - Circuit breaker patterns for preventing cascading failures
        - Fallback notification channels for redundancy
        - Failure rate monitoring and cooldown periods
        - Channel-specific failure tracking

        This method is called during AlertManagerAgent initialization to ensure
        robust error handling and system resilience.
        """
        self.error_recovery_config = {
            'max_consecutive_failures': 5,
            'failure_cooldown_minutes': 15,
            'fallback_channels': {
                NotificationChannel.EMAIL: [NotificationChannel.MQTT],
                NotificationChannel.MQTT: [NotificationChannel.HTTP],
                NotificationChannel.HTTP: [NotificationChannel.EMAIL]
            },
            'circuit_breaker_threshold': 10,
            'circuit_breaker_timeout_minutes': 30
        }

        self.error_tracking = {
            'consecutive_failures': 0,
            'last_failure_time': None,
            'circuit_breaker_open': False,
            'circuit_breaker_open_time': None,
            'channel_failures': {channel: 0 for channel in NotificationChannel}
        }

    def _log_alert_processing_metrics(self) -> None:
        """
        Log comprehensive alert processing metrics for monitoring.

        Logs structured JSON metrics including:
        - Current alert counts and status distribution
        - Notification delivery statistics and success rates
        - Error tracking and circuit breaker status
        - Processing performance metrics

        These metrics are designed for consumption by monitoring systems
        and can be used for alerting, dashboards, and performance analysis.

        The metrics are logged at INFO level with the prefix 'ALERT_METRICS:'
        to facilitate log parsing and monitoring system integration.
        """
        try:
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "company": self.company_name,
                "active_alerts": len(self.active_alerts),
                "notification_history_count": len(self.notification_history),
                "processing_metrics": self.processing_metrics,
                "error_tracking": getattr(self, 'error_tracking', {}),
                "circuit_breaker_status": "open" if getattr(self, 'error_tracking', {}).get('circuit_breaker_open', False) else "closed"
            }

            # Log as structured JSON for monitoring systems
            logger.info(f"ALERT_METRICS: {json.dumps(metrics)}")

        except Exception as e:
            logger.error(f"Failed to log alert processing metrics: {e}")

    async def _graceful_degradation_check(self) -> Dict[str, bool]:
        """Check system health and enable graceful degradation if needed."""
        health_status = {
            "notification_system_healthy": True,
            "mcp_servers_available": True,
            "data_processing_healthy": True,
            "degraded_mode_active": False
        }

        try:
            # Check circuit breaker status
            error_tracking = getattr(self, 'error_tracking', {})
            if error_tracking.get('circuit_breaker_open', False):
                health_status["notification_system_healthy"] = False
                health_status["degraded_mode_active"] = True

            # Check recent failure rates
            recent_failures = sum(1 for delivery in self.notification_history[-10:]
                                if delivery.status == "failed")
            if recent_failures > 7:  # >70% failure rate in last 10 notifications
                health_status["notification_system_healthy"] = False
                health_status["degraded_mode_active"] = True

            # Check MCP server availability (placeholder)
            # In real implementation, this would ping MCP servers
            health_status["mcp_servers_available"] = True

            # Log health status
            if health_status["degraded_mode_active"]:
                logger.warning("Alert system operating in degraded mode")
                logger.warning(f"Health status: {health_status}")

            return health_status

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "notification_system_healthy": False,
                "mcp_servers_available": False,
                "data_processing_healthy": False,
                "degraded_mode_active": True
            }

    def _get_channels_for_severity(self, severity: AlertSeverity) -> List[str]:
        """Get notification channels based on alert severity."""
        if severity == AlertSeverity.CRITICAL:
            return ['email', 'mqtt', 'http']
        elif severity == AlertSeverity.HIGH:
            return ['email', 'mqtt']
        elif severity == AlertSeverity.MEDIUM:
            return ['email']
        else:  # LOW
            return ['mqtt']


    async def send_notifications(self, alerts: List[Alert]) -> List[NotificationDelivery]:
        """
        Send notifications for alerts through configured channels with concurrent processing.

        Args:
            alerts: List of alerts to send notifications for

        Returns:
            List of notification delivery results
        """
        try:
            # Use concurrent notification processing for better performance
            deliveries = await self._send_notifications_concurrently(alerts)

            # Add all deliveries to history
            self.notification_history.extend(deliveries)

            # Log summary
            successful = len([d for d in deliveries if d.status == "success"])
            failed = len([d for d in deliveries if d.status == "failed"])
            logger.info(f"Notification batch completed: {successful} successful, {failed} failed")

            return deliveries

        except Exception as e:
            logger.error(f"Critical error in notification processing: {e}")

            # Create error deliveries for all alerts
            error_deliveries = []
            for alert in alerts:
                channels = self._determine_notification_channels_by_priority(alert)
                for channel in channels:
                    error_delivery = NotificationDelivery(
                        alert_id=alert.id,
                        channel=channel,
                        recipient=self._get_recipient_for_channel(channel),
                        status="failed",
                        timestamp=datetime.now(),
                        error_message=f"Critical notification system error: {str(e)}"
                    )
                    error_deliveries.append(error_delivery)

            self.notification_history.extend(error_deliveries)
            return error_deliveries

    async def _send_via_mcp_channel(self, alert: Alert, channel: NotificationChannel, recipient: str) -> bool:
        """Send notification via MCP channel with actual MCP integration."""
        try:
            # Format message for the specific channel
            formatted_message = self._format_message_for_channel(alert, channel)

            # Send via appropriate MCP tool
            if channel == NotificationChannel.EMAIL:
                return await self._send_email_notification(alert, formatted_message, recipient)
            elif channel == NotificationChannel.MQTT:
                return await self._send_mqtt_notification(alert, formatted_message, recipient)
            elif channel == NotificationChannel.HTTP:
                return await self._send_http_notification(alert, formatted_message, recipient)
            else:
                logger.error(f"Unsupported notification channel: {channel}")
                return False

        except Exception as e:
            logger.error(f"MCP channel {channel.value} delivery failed: {e}")
            return False

    def _format_message_for_channel(self, alert: Alert, channel: NotificationChannel) -> Dict[str, str]:
        """Format alert message for specific notification channel."""
        # Create priority-aware subject line
        severity_text = alert.severity.value.upper()
        if alert.severity in [AlertSeverity.HIGH, AlertSeverity.CRITICAL]:
            priority_text = f"{severity_text} PRIORITY"
        else:
            priority_text = severity_text

        base_subject = f"[{priority_text}] {alert.company_name} - {alert.alert_type.value.title()} Alert"

        if channel == NotificationChannel.EMAIL:
            # Email format with HTML-friendly content
            return {
                "subject": base_subject,
                "content": self._format_email_content(alert)
            }
        elif channel == NotificationChannel.MQTT:
            # MQTT format with JSON payload
            return {
                "subject": base_subject,
                "content": self._format_mqtt_content(alert)
            }
        elif channel == NotificationChannel.HTTP:
            # HTTP webhook format
            return {
                "subject": base_subject,
                "content": self._format_http_content(alert)
            }
        else:
            return {
                "subject": base_subject,
                "content": alert.message
            }

    def _format_email_content(self, alert: Alert) -> str:
        """Format alert content for email delivery."""
        content = f"""
ALERT NOTIFICATION - {alert.company_name}

Alert ID: {alert.id}
Timestamp: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}
Severity: {alert.severity.value.upper()}
Type: {alert.alert_type.value.title()}

ALERT DETAILS:
{alert.message}

ESCALATION TIMELINE:
"""

        # Add escalation timeline
        timeline = self._generate_escalation_timeline(alert)
        for phase, description in timeline.items():
            content += f"• {phase.title()}: {description}\n"

        content += f"""

ALERT DATA:
Threshold Value: {alert.threshold_value}
Actual Value: {alert.actual_value}
Variance: {((alert.actual_value - alert.threshold_value) / alert.threshold_value * 100):.1f}%

For more information, please contact the {alert.alert_type.value} team.

This is an automated alert from the Financial Analysis System.
"""
        return content

    def _format_mqtt_content(self, alert: Alert) -> str:
        """Format alert content for MQTT delivery."""
        mqtt_payload = {
            "alert_id": alert.id,
            "company": alert.company_name,
            "timestamp": alert.timestamp.isoformat(),
            "severity": alert.severity.value,
            "type": alert.alert_type.value,
            "title": alert.title,
            "threshold": alert.threshold_value,
            "actual": alert.actual_value,
            "status": alert.status.value,
            "message": alert.message[:500],  # Truncate for MQTT
            "metadata": alert.metadata
        }
        return json.dumps(mqtt_payload)

    def _format_http_content(self, alert: Alert) -> str:
        """Format alert content for HTTP webhook delivery."""
        webhook_payload = {
            "event_type": "financial_alert",
            "alert": {
                "id": alert.id,
                "company_name": alert.company_name,
                "timestamp": alert.timestamp.isoformat(),
                "severity": alert.severity.value,
                "alert_type": alert.alert_type.value,
                "title": alert.title,
                "message": alert.message,
                "threshold_value": alert.threshold_value,
                "actual_value": alert.actual_value,
                "status": alert.status.value,
                "data": alert.data,
                "metadata": alert.metadata
            },
            "notification": {
                "channels": self._get_channels_for_severity(alert.severity),
                "escalation": self._generate_escalation_timeline(alert)
            }
        }
        return json.dumps(webhook_payload)

    async def _send_email_notification(self, alert: Alert, formatted_message: Dict[str, str], recipient: str) -> bool:
        """Send email notification via MCP EmailNotification tool using direct tool calling."""
        try:
            logger.info(f"Email notification blocked for alert {alert.id} to {recipient}")
            return True  # Email notifications disabled for this demo

            # Ensure agent is initialized for MCP operations
            if not self.initialized:
                await self.initialize()

            # Extract string values from formatted_message dictionary
            if isinstance(formatted_message["content"], dict):
                # If content is a dict, extract the main message or convert to string
                content_str = formatted_message["content"].get("message", str(formatted_message["content"]))
            else:
                content_str = str(formatted_message["content"])

            if isinstance(formatted_message["subject"], dict):
                # If subject is a dict, extract the main subject or convert to string
                subject_str = formatted_message["subject"].get("subject", str(formatted_message["subject"]))
            else:
                subject_str = str(formatted_message["subject"])

            # Use direct MCP tool calling with proper string parameters
            # The Agent Develop notification server expects alert_message parameter
            tool_arguments = {
                "alert_message": content_str,
                "subject": subject_str,
                "content": content_str,
                "recipient": recipient
            }

            # Debug logging to see what we're sending
            logger.debug(f"Direct MCP tool call - EmailNotification arguments: {tool_arguments}")
            logger.debug(f"Content type: {type(content_str)}, Subject type: {type(subject_str)}")

            # Call EmailNotification tool directly via MCP
            result = await self.call_tool(
                name="EmailNotification",
                arguments=tool_arguments,
                server_name="alert-notification"
            )

            # Check if the call was successful
            if result.isError:
                error_msg = "Unknown error"
                if result.content:
                    error_msg = str(result.content[0].text) if hasattr(result.content[0], 'text') else str(result.content[0])
                logger.error(f"EmailNotification tool call failed: {error_msg}")
                return False

            # Extract success message from result
            success_msg = "Tool executed successfully"
            if result.content:
                success_msg = str(result.content[0].text) if hasattr(result.content[0], 'text') else str(result.content[0])

            logger.info(f"Email notification sent successfully for alert {alert.id}: {success_msg}")
            return True

        except Exception as e:
            logger.error(f"Email notification failed for alert {alert.id}: {e}")
            return False

    async def _send_mqtt_notification(self, alert: Alert, formatted_message: Dict[str, str], topic: str) -> bool:
        """Send MQTT notification via MCP MqttNotification tool using direct tool calling."""
        try:
            logger.info(f"Sending MQTT notification for alert {alert.id} to topic {topic}")

            # Ensure agent is initialized for MCP operations
            if not self.initialized:
                await self.initialize()

            # Extract string values from formatted_message dictionary
            # For MQTT, the content is a JSON string that needs to be parsed to extract the actual message
            content_str = str(formatted_message["content"])
            if content_str.startswith('{"'):
                # Parse JSON to extract the actual alert message
                try:
                    import json
                    content_data = json.loads(content_str)
                    # Extract the main message from the JSON structure
                    content_str = content_data.get("message", content_str)
                except (json.JSONDecodeError, AttributeError):
                    # If parsing fails, use the original string
                    pass

            subject_str = str(formatted_message["subject"])

            # Use direct MCP tool calling with proper string parameters
            # The Agent Develop notification server expects alert_message parameter
            tool_arguments = {
                "alert_message": content_str,
                "subject": subject_str,
                "content": content_str
            }

            # Debug logging to see what we're sending
            logger.info(f"MQTT formatted_message structure: {formatted_message}")
            logger.info(f"MQTT content_str extracted: {content_str}")
            logger.info(f"MQTT subject_str extracted: {subject_str}")
            logger.debug(f"Direct MCP tool call - MqttNotification arguments: {tool_arguments}")
            logger.debug(f"Content type: {type(content_str)}, Subject type: {type(subject_str)}")

            # Call MqttNotification tool directly via MCP
            result = await self.call_tool(
                name="MqttNotification",
                arguments=tool_arguments,
                server_name="alert-notification"
            )

            # Check if the call was successful
            if result.isError:
                error_msg = "Unknown error"
                if result.content:
                    error_msg = str(result.content[0].text) if hasattr(result.content[0], 'text') else str(result.content[0])
                logger.error(f"MqttNotification tool call failed: {error_msg}")
                return False

            # Extract success message from result
            success_msg = "Tool executed successfully"
            if result.content:
                success_msg = str(result.content[0].text) if hasattr(result.content[0], 'text') else str(result.content[0])

            logger.info(f"MQTT notification sent successfully for alert {alert.id}: {success_msg}")
            return True

        except Exception as e:
            logger.error(f"MQTT notification failed for alert {alert.id}: {e}")
            return False

    async def _send_http_notification(self, alert: Alert, formatted_message: Dict[str, str], webhook_url: str) -> bool:
        """Send HTTP notification via MCP HttpNotification tool using direct tool calling."""
        try:
            logger.info(f"Sending HTTP notification for alert {alert.id} to {webhook_url}")

            # Ensure agent is initialized for MCP operations
            if not self.initialized:
                await self.initialize()

            # Extract string values from formatted_message dictionary
            # For HTTP, the content is a JSON string that needs to be parsed to extract the actual message
            content_str = str(formatted_message["content"])
            if content_str.startswith('{"'):
                # Parse JSON to extract the actual alert message
                try:
                    import json
                    content_data = json.loads(content_str)
                    # Extract the main message from the JSON structure
                    if "alert" in content_data and "message" in content_data["alert"]:
                        content_str = content_data["alert"]["message"]
                    else:
                        content_str = content_data.get("message", content_str)
                except (json.JSONDecodeError, AttributeError):
                    # If parsing fails, use the original string
                    pass

            subject_str = str(formatted_message["subject"])

            # Use direct MCP tool calling with proper string parameters
            # The Agent Develop notification server expects alert_message parameter
            tool_arguments = {
                "alert_message": content_str,
                "subject": subject_str,
                "content": content_str
            }

            # Debug logging to see what we're sending
            logger.info(f"HTTP formatted_message structure: {formatted_message}")
            logger.info(f"HTTP content_str extracted: {content_str}")
            logger.info(f"HTTP subject_str extracted: {subject_str}")
            logger.debug(f"Direct MCP tool call - HttpNotification arguments: {tool_arguments}")
            logger.debug(f"Content type: {type(content_str)}, Subject type: {type(subject_str)}")

            # Call HttpNotification tool directly via MCP
            result = await self.call_tool(
                name="HttpNotification",
                arguments=tool_arguments,
                server_name="alert-notification"
            )

            # Check if the call was successful
            if result.isError:
                error_msg = "Unknown error"
                if result.content:
                    error_msg = str(result.content[0].text) if hasattr(result.content[0], 'text') else str(result.content[0])
                logger.error(f"HttpNotification tool call failed: {error_msg}")
                return False

            # Extract success message from result
            success_msg = "Tool executed successfully"
            if result.content:
                success_msg = str(result.content[0].text) if hasattr(result.content[0], 'text') else str(result.content[0])

            logger.info(f"HTTP notification sent successfully for alert {alert.id}: {success_msg}")
            return True

        except Exception as e:
            logger.error(f"HTTP notification failed for alert {alert.id}: {e}")
            return False

    def _get_recipient_for_channel(self, channel: NotificationChannel) -> str:
        """Get recipient address for notification channel."""
        if channel == NotificationChannel.EMAIL:
            recipients = self.alert_config.get('email_recipients', ['<EMAIL>'])
            return recipients[0] if recipients else '<EMAIL>'
        elif channel == NotificationChannel.MQTT:
            return self.alert_config.get('mqtt_topic', '/financial/alerts')
        elif channel == NotificationChannel.HTTP:
            return self.alert_config.get('webhook_url', 'http://localhost:5000/alert')
        else:
            return 'unknown'

    async def acknowledge_alert(self, alert_id: str, user_id: str, acknowledgment_notes: str = "") -> Dict[str, Any]:
        """
        Acknowledge an alert and update its status with comprehensive tracking.

        Args:
            alert_id: Unique alert identifier
            user_id: User acknowledging the alert
            acknowledgment_notes: Optional notes about the acknowledgment

        Returns:
            Dictionary with acknowledgment result and details
        """
        if alert_id not in self.active_alerts:
            return {
                "success": False,
                "error": f"Alert {alert_id} not found",
                "alert_id": alert_id
            }

        alert = self.active_alerts[alert_id]

        # Check if alert is in a state that can be acknowledged
        if alert.status in [AlertStatus.RESOLVED]:
            return {
                "success": False,
                "error": f"Alert {alert_id} is already resolved and cannot be acknowledged",
                "alert_id": alert_id,
                "current_status": alert.status.value
            }

        # Update alert with acknowledgment information
        previous_status = alert.status
        alert.status = AlertStatus.ACKNOWLEDGED
        alert.acknowledgment_user = user_id
        alert.acknowledgment_time = datetime.now()

        # Add acknowledgment tracking to metadata
        alert.metadata.update({
            'acknowledgment_notes': acknowledgment_notes,
            'acknowledgment_timestamp': alert.acknowledgment_time.isoformat(),
            'previous_status': previous_status.value,
            'acknowledgment_method': 'api_call'  # Could be extended for different methods
        })

        # Log acknowledgment with details
        logger.info(f"Alert {alert_id} acknowledged by {user_id} at {alert.acknowledgment_time}")
        if acknowledgment_notes:
            logger.info(f"Acknowledgment notes for {alert_id}: {acknowledgment_notes}")

        # Update processing metrics
        self.processing_metrics['alerts_acknowledged'] = self.processing_metrics.get('alerts_acknowledged', 0) + 1

        # Send acknowledgment notification to relevant stakeholders
        await self._send_acknowledgment_notification(alert, user_id, acknowledgment_notes)

        return {
            "success": True,
            "alert_id": alert_id,
            "acknowledged_by": user_id,
            "acknowledged_at": alert.acknowledgment_time.isoformat(),
            "previous_status": previous_status.value,
            "current_status": alert.status.value,
            "notes": acknowledgment_notes
        }

    async def resolve_alert(self, alert_id: str, user_id: str, resolution_notes: str = "",
                          resolution_actions: List[str] = None) -> Dict[str, Any]:
        """
        Mark an alert as resolved with comprehensive tracking.

        Args:
            alert_id: Unique alert identifier
            user_id: User resolving the alert
            resolution_notes: Notes about the resolution
            resolution_actions: List of actions taken to resolve the alert

        Returns:
            Dictionary with resolution result and details
        """
        if alert_id not in self.active_alerts:
            return {
                "success": False,
                "error": f"Alert {alert_id} not found",
                "alert_id": alert_id
            }

        alert = self.active_alerts[alert_id]

        # Check if alert is in a state that can be resolved
        if alert.status == AlertStatus.RESOLVED:
            return {
                "success": False,
                "error": f"Alert {alert_id} is already resolved",
                "alert_id": alert_id,
                "resolved_at": alert.resolution_time.isoformat() if alert.resolution_time else None,
                "resolved_by": alert.metadata.get('resolution_user')
            }

        # Calculate resolution time
        resolution_time = datetime.now()
        time_to_resolution = (resolution_time - alert.timestamp).total_seconds()

        # Update alert with resolution information
        previous_status = alert.status
        alert.status = AlertStatus.RESOLVED
        alert.resolution_time = resolution_time

        # Add comprehensive resolution tracking to metadata
        alert.metadata.update({
            'resolution_user': user_id,
            'resolution_notes': resolution_notes,
            'resolution_timestamp': resolution_time.isoformat(),
            'resolution_actions': resolution_actions or [],
            'time_to_resolution_seconds': time_to_resolution,
            'time_to_resolution_hours': time_to_resolution / 3600,
            'previous_status': previous_status.value,
            'resolution_method': 'api_call'
        })

        # Log resolution with details
        logger.info(f"Alert {alert_id} resolved by {user_id} at {resolution_time}")
        logger.info(f"Time to resolution: {time_to_resolution/3600:.2f} hours")
        if resolution_notes:
            logger.info(f"Resolution notes for {alert_id}: {resolution_notes}")
        if resolution_actions:
            logger.info(f"Resolution actions for {alert_id}: {', '.join(resolution_actions)}")

        # Update processing metrics
        self.processing_metrics['alerts_resolved'] = self.processing_metrics.get('alerts_resolved', 0) + 1
        self.processing_metrics['avg_resolution_time_hours'] = self._calculate_average_resolution_time()

        # Send resolution notification to relevant stakeholders
        await self._send_resolution_notification(alert, user_id, resolution_notes, resolution_actions)

        # Archive resolved alert (move from active to resolved)
        await self._archive_resolved_alert(alert)

        return {
            "success": True,
            "alert_id": alert_id,
            "resolved_by": user_id,
            "resolved_at": resolution_time.isoformat(),
            "time_to_resolution_hours": time_to_resolution / 3600,
            "previous_status": previous_status.value,
            "current_status": alert.status.value,
            "resolution_notes": resolution_notes,
            "resolution_actions": resolution_actions or []
        }

    async def _send_acknowledgment_notification(self, alert: Alert, user_id: str, notes: str):
        """Send notification about alert acknowledgment to stakeholders."""
        try:
            # Create acknowledgment notification message
            ack_message = f"""
Alert Acknowledgment Notification

Alert ID: {alert.id}
Company: {alert.company_name}
Alert Type: {alert.alert_type.value.title()}
Severity: {alert.severity.value.upper()}
Acknowledged By: {user_id}
Acknowledged At: {alert.acknowledgment_time.strftime('%Y-%m-%d %H:%M:%S UTC')}

Original Alert: {alert.title}

Acknowledgment Notes: {notes if notes else 'No additional notes provided'}

The alert is now being actively managed and will be resolved once corrective actions are completed.
"""

            # Send to low-priority channels (typically MQTT for system tracking)
            logger.info(f"Sending acknowledgment notification for alert {alert.id}")
            # Implementation would send via MQTT or other tracking channels

        except Exception as e:
            logger.error(f"Failed to send acknowledgment notification for alert {alert.id}: {e}")

    async def _send_resolution_notification(self, alert: Alert, user_id: str, notes: str, actions: List[str]):
        """Send notification about alert resolution to stakeholders."""
        try:
            # Create resolution notification message
            resolution_message = f"""
Alert Resolution Notification

Alert ID: {alert.id}
Company: {alert.company_name}
Alert Type: {alert.alert_type.value.title()}
Severity: {alert.severity.value.upper()}
Resolved By: {user_id}
Resolved At: {alert.resolution_time.strftime('%Y-%m-%d %H:%M:%S UTC')}
Time to Resolution: {alert.metadata.get('time_to_resolution_hours', 0):.2f} hours

Original Alert: {alert.title}

Resolution Notes: {notes if notes else 'No additional notes provided'}

Actions Taken:
"""

            if actions:
                for i, action in enumerate(actions, 1):
                    resolution_message += f"{i}. {action}\n"
            else:
                resolution_message += "No specific actions documented\n"

            resolution_message += "\nThe alert has been successfully resolved and is now closed."

            # Send to appropriate channels based on original alert severity
            logger.info(f"Sending resolution notification for alert {alert.id}")
            # Implementation would send via configured channels

        except Exception as e:
            logger.error(f"Failed to send resolution notification for alert {alert.id}: {e}")

    async def _archive_resolved_alert(self, alert: Alert):
        """Archive resolved alert for historical tracking."""
        try:
            # In a production system, this would persist to database
            # For now, we'll keep it in memory but mark as archived
            alert.metadata['archived'] = True
            alert.metadata['archived_at'] = datetime.now().isoformat()

            logger.info(f"Alert {alert.id} archived successfully")

        except Exception as e:
            logger.error(f"Failed to archive alert {alert.id}: {e}")

    def _calculate_average_resolution_time(self) -> float:
        """Calculate average resolution time for resolved alerts."""
        resolved_alerts = [
            alert for alert in self.active_alerts.values()
            if alert.status == AlertStatus.RESOLVED and 'time_to_resolution_hours' in alert.metadata
        ]

        if not resolved_alerts:
            return 0.0

        total_time = sum(alert.metadata['time_to_resolution_hours'] for alert in resolved_alerts)
        return total_time / len(resolved_alerts)

    async def _execute_with_timeout_and_retry(self,
                                            operation_name: str,
                                            async_operation,
                                            timeout_seconds: float = 30.0,
                                            max_retries: int = 3,
                                            retry_delay: float = 1.0) -> Any:
        """
        Execute async operation with timeout and retry logic.

        Args:
            operation_name: Name of the operation for logging
            async_operation: Async function to execute
            timeout_seconds: Timeout in seconds
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds

        Returns:
            Result of the async operation

        Raises:
            Exception: If all retries fail or timeout occurs
        """
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                # Execute with timeout
                result = await asyncio.wait_for(async_operation(), timeout=timeout_seconds)

                if attempt > 0:
                    logger.info(f"{operation_name} succeeded on attempt {attempt + 1}")

                return result

            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"{operation_name} timed out on attempt {attempt + 1}/{max_retries + 1}")

            except Exception as e:
                last_exception = e
                logger.warning(f"{operation_name} failed on attempt {attempt + 1}/{max_retries + 1}: {str(e)}")

            # Don't delay after the last attempt
            if attempt < max_retries:
                await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff

        # All retries failed
        logger.error(f"{operation_name} failed after {max_retries + 1} attempts")
        raise last_exception

    async def _send_notifications_concurrently(self, alerts: List[Alert]) -> List[NotificationDelivery]:
        """
        Send notifications for multiple alerts concurrently with proper error handling.

        Args:
            alerts: List of alerts to send notifications for

        Returns:
            List of notification delivery results
        """
        # Create concurrent tasks for all notifications
        notification_tasks = []

        for alert in alerts:
            channels = self._determine_notification_channels_by_priority(alert)

            for channel in channels:
                recipient = self._get_recipient_for_channel(channel)

                # Create async task for each notification
                task = asyncio.create_task(
                    self._send_single_notification_with_retry(alert, channel, recipient),
                    name=f"notify_{alert.id}_{channel.value}"
                )
                notification_tasks.append(task)

        # Execute all notifications concurrently with improved timeout handling
        try:
            # Calculate dynamic timeout based on number of tasks and retry configuration
            max_retries = self.alert_config.get('retry_attempts', 3)
            retry_delay = self.alert_config.get('retry_delay_seconds', 5)

            # Conservative timeout calculation:
            # (individual timeout + max retry time) * safety factor
            individual_timeout = 30.0
            max_retry_time = retry_delay * (2 ** max_retries)  # Exponential backoff
            safety_factor = 1.5
            dynamic_timeout = (individual_timeout + max_retry_time) * safety_factor

            # Minimum 120 seconds, maximum 300 seconds
            batch_timeout = max(10.0, min(dynamic_timeout, 300.0))

            logger.info(f"Starting notification batch with {len(notification_tasks)} tasks, timeout: {batch_timeout}s")

            # Wait for all notifications to complete or timeout
            results = await asyncio.wait_for(
                asyncio.gather(*notification_tasks, return_exceptions=True),
                timeout=batch_timeout
            )

            # Process results and handle exceptions
            deliveries = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # Create failed delivery record
                    task_name = notification_tasks[i].get_name()
                    logger.error(f"Notification task {task_name} failed: {result}")

                    # Extract alert_id and channel from task name
                    parts = task_name.split('_')
                    if len(parts) >= 3:
                        alert_id = '_'.join(parts[1:-1])
                        channel_name = parts[-1]

                        failed_delivery = NotificationDelivery(
                            alert_id=alert_id,
                            channel=NotificationChannel(channel_name),
                            recipient="unknown",
                            status="failed",
                            timestamp=datetime.now(),
                            error_message=str(result)
                        )
                        deliveries.append(failed_delivery)
                else:
                    # Successful delivery
                    deliveries.append(result)

            return deliveries

        except asyncio.TimeoutError:
            logger.error(f"Notification batch timed out after {batch_timeout} seconds")

            # Cancel remaining tasks gracefully
            cancelled_count = 0
            for task in notification_tasks:
                if not task.done():
                    task.cancel()
                    cancelled_count += 1

            logger.warning(f"Cancelled {cancelled_count} pending notification tasks due to timeout")

            # Collect results from completed tasks and create timeout records for others
            deliveries = []
            for task in notification_tasks:
                if task.done() and not task.cancelled():
                    try:
                        result = task.result()
                        deliveries.append(result)
                    except Exception as e:
                        # Handle completed but failed tasks
                        task_name = task.get_name()
                        parts = task_name.split('_')
                        if len(parts) >= 3:
                            alert_id = '_'.join(parts[1:-1])
                            channel_name = parts[-1]

                            failed_delivery = NotificationDelivery(
                                alert_id=alert_id,
                                channel=NotificationChannel(channel_name),
                                recipient="unknown",
                                status="failed",
                                timestamp=datetime.now(),
                                error_message=f"Task completed with error: {str(e)}"
                            )
                            deliveries.append(failed_delivery)
                else:
                    # Create timeout delivery record for cancelled/incomplete tasks
                    task_name = task.get_name()
                    parts = task_name.split('_')
                    if len(parts) >= 3:
                        alert_id = '_'.join(parts[1:-1])
                        channel_name = parts[-1]

                        timeout_delivery = NotificationDelivery(
                            alert_id=alert_id,
                            channel=NotificationChannel(channel_name),
                            recipient="unknown",
                            status="failed",
                            timestamp=datetime.now(),
                            error_message="Notification timed out or was cancelled"
                        )
                        deliveries.append(timeout_delivery)

            return deliveries

    async def _send_single_notification_with_retry(self, alert: Alert, channel: NotificationChannel, recipient: str) -> NotificationDelivery:
        """
        Send a single notification with retry logic.

        Args:
            alert: Alert to send notification for
            channel: Notification channel
            recipient: Recipient address

        Returns:
            NotificationDelivery result
        """
        start_time = datetime.now()

        try:
            # Check circuit breaker for this channel
            if self._is_circuit_breaker_open(channel):
                logger.warning(f"Circuit breaker open for {channel.value}, skipping notification")
                return NotificationDelivery(
                    alert_id=alert.id,
                    channel=channel,
                    recipient=recipient,
                    status="failed",
                    timestamp=datetime.now(),
                    error_message="Circuit breaker open - channel temporarily disabled",
                    delivery_time_ms=0
                )

            # Get retry configuration with channel-specific adjustments
            max_retries = self.alert_config.get('retry_attempts', 3)
            retry_delay = self.alert_config.get('retry_delay_seconds', 5)

            # Reduce retries for email if authentication issues are detected
            if channel == NotificationChannel.EMAIL:
                max_retries = min(max_retries, 1)  # Only 1 retry for email
                timeout_seconds = 15.0  # Shorter timeout for email
            else:
                timeout_seconds = 30.0

            # Execute notification with retry
            success = await self._execute_with_timeout_and_retry(
                operation_name=f"notification_{alert.id}_{channel.value}",
                async_operation=lambda: self._send_via_mcp_channel(alert, channel, recipient),
                timeout_seconds=timeout_seconds,
                max_retries=max_retries,
                retry_delay=retry_delay
            )

            delivery_time = int((datetime.now() - start_time).total_seconds() * 1000)

            delivery = NotificationDelivery(
                alert_id=alert.id,
                channel=channel,
                recipient=recipient,
                status="success" if success else "failed",
                timestamp=datetime.now(),
                delivery_time_ms=delivery_time
            )

            if not success:
                delivery.error_message = "MCP notification delivery failed after retries"
                # Record failure for circuit breaker
                self._record_notification_failure(channel, delivery.error_message)

            return delivery

        except Exception as e:
            delivery_time = int((datetime.now() - start_time).total_seconds() * 1000)
            error_message = str(e)

            # Record failure for circuit breaker
            self._record_notification_failure(channel, error_message)

            return NotificationDelivery(
                alert_id=alert.id,
                channel=channel,
                recipient=recipient,
                status="failed",
                timestamp=datetime.now(),
                error_message=error_message,
                delivery_time_ms=delivery_time
            )

    async def _batch_process_alerts_async(self, alerts: List[Alert], batch_size: int = 5) -> AlertProcessingResult:
        """
        Process alerts in batches asynchronously to avoid overwhelming the system.

        Args:
            alerts: List of alerts to process
            batch_size: Number of alerts to process concurrently

        Returns:
            Combined processing result
        """
        start_time = datetime.now()
        all_notifications = []
        all_errors = []

        # Process alerts in batches
        for i in range(0, len(alerts), batch_size):
            batch = alerts[i:i + batch_size]
            logger.info(f"Processing alert batch {i//batch_size + 1}: {len(batch)} alerts")

            try:
                # Process batch concurrently
                batch_notifications = await self._send_notifications_concurrently(batch)
                all_notifications.extend(batch_notifications)

                # Record notification failures as errors
                failed_notifications = [n for n in batch_notifications if n.status == "failed"]
                for failed_notification in failed_notifications:
                    error_msg = f"Notification failed for alert {failed_notification.alert_id} via {failed_notification.channel.value}: {failed_notification.error_message}"
                    all_errors.append(error_msg)

                # Update alert statuses
                for alert in batch:
                    alert_notifications = [n for n in batch_notifications if n.alert_id == alert.id]
                    if any(n.status == "success" for n in alert_notifications):
                        alert.status = AlertStatus.SENT
                    else:
                        alert.status = AlertStatus.FAILED

                # Small delay between batches to avoid overwhelming downstream systems
                if i + batch_size < len(alerts):
                    await asyncio.sleep(0.5)

            except Exception as e:
                error_msg = f"Batch processing failed for alerts {i}-{i+len(batch)}: {str(e)}"
                all_errors.append(error_msg)
                logger.error(error_msg)

                # Mark batch alerts as failed
                for alert in batch:
                    alert.status = AlertStatus.FAILED

        # Update metrics
        successful_notifications = len([n for n in all_notifications if n.status == "success"])
        failed_notifications = len([n for n in all_notifications if n.status == "failed"])

        self.processing_metrics["notifications_sent"] += successful_notifications
        self.processing_metrics["notifications_failed"] += failed_notifications

        # Generate summary
        summary = self._generate_processing_summary(alerts, all_notifications, all_errors)
        processing_time = int((datetime.now() - start_time).total_seconds() * 1000)

        return AlertProcessingResult(
            processed_alerts=alerts,
            notifications_sent=all_notifications,
            errors=all_errors,
            summary=summary,
            processing_time_ms=processing_time
        )

    def get_alert_history(self, alert_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive history and status of an alert."""
        if alert_id not in self.active_alerts:
            return None

        alert = self.active_alerts[alert_id]

        # Get notification delivery history for this alert
        alert_notifications = [
            {
                "channel": delivery.channel.value,
                "recipient": delivery.recipient,
                "status": delivery.status,
                "timestamp": delivery.timestamp.isoformat(),
                "error_message": delivery.error_message,
                "delivery_time_ms": delivery.delivery_time_ms
            }
            for delivery in self.notification_history
            if delivery.alert_id == alert_id
        ]

        # Calculate alert lifecycle metrics
        current_time = datetime.now()
        time_since_creation = (current_time - alert.timestamp).total_seconds()

        history = {
            "alert_id": alert.id,
            "company_name": alert.company_name,
            "alert_type": alert.alert_type.value,
            "severity": alert.severity.value,
            "current_status": alert.status.value,
            "title": alert.title,
            "created_at": alert.timestamp.isoformat(),
            "threshold_value": alert.threshold_value,
            "actual_value": alert.actual_value,
            "time_since_creation_hours": time_since_creation / 3600,
            "lifecycle": {
                "created": alert.timestamp.isoformat(),
                "acknowledged": alert.acknowledgment_time.isoformat() if alert.acknowledgment_time else None,
                "acknowledged_by": alert.acknowledgment_user,
                "resolved": alert.resolution_time.isoformat() if alert.resolution_time else None,
                "resolved_by": alert.metadata.get('resolution_user')
            },
            "notifications": alert_notifications,
            "metadata": alert.metadata,
            "data": alert.data
        }

        return history

    def get_alerts_by_status(self, status: AlertStatus) -> List[Dict[str, Any]]:
        """Get all alerts with a specific status."""
        matching_alerts = [
            alert for alert in self.active_alerts.values()
            if alert.status == status
        ]

        return [
            {
                "alert_id": alert.id,
                "company_name": alert.company_name,
                "alert_type": alert.alert_type.value,
                "severity": alert.severity.value,
                "title": alert.title,
                "created_at": alert.timestamp.isoformat(),
                "status": alert.status.value,
                "acknowledged_by": alert.acknowledgment_user,
                "acknowledged_at": alert.acknowledgment_time.isoformat() if alert.acknowledgment_time else None
            }
            for alert in matching_alerts
        ]

    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get comprehensive alert statistics and metrics."""
        total_alerts = len(self.active_alerts)

        # Status breakdown
        status_counts = {}
        for status in AlertStatus:
            status_counts[status.value] = len([
                alert for alert in self.active_alerts.values()
                if alert.status == status
            ])

        # Severity breakdown
        severity_counts = {}
        for severity in AlertSeverity:
            severity_counts[severity.value] = len([
                alert for alert in self.active_alerts.values()
                if alert.severity == severity
            ])

        # Type breakdown
        type_counts = {}
        for alert_type in AlertType:
            type_counts[alert_type.value] = len([
                alert for alert in self.active_alerts.values()
                if alert.alert_type == alert_type
            ])

        # Resolution metrics
        resolved_alerts = [
            alert for alert in self.active_alerts.values()
            if alert.status == AlertStatus.RESOLVED
        ]

        avg_resolution_time = 0.0
        if resolved_alerts:
            total_resolution_time = sum(
                alert.metadata.get('time_to_resolution_hours', 0)
                for alert in resolved_alerts
            )
            avg_resolution_time = total_resolution_time / len(resolved_alerts)

        # Notification statistics
        total_notifications = len(self.notification_history)
        successful_notifications = len([
            n for n in self.notification_history
            if n.status == "success"
        ])
        failed_notifications = len([
            n for n in self.notification_history
            if n.status == "failed"
        ])

        return {
            "total_alerts": total_alerts,
            "status_breakdown": status_counts,
            "severity_breakdown": severity_counts,
            "type_breakdown": type_counts,
            "resolution_metrics": {
                "total_resolved": len(resolved_alerts),
                "average_resolution_time_hours": avg_resolution_time,
                "resolution_rate": len(resolved_alerts) / total_alerts if total_alerts > 0 else 0
            },
            "notification_metrics": {
                "total_notifications": total_notifications,
                "successful_notifications": successful_notifications,
                "failed_notifications": failed_notifications,
                "success_rate": successful_notifications / total_notifications if total_notifications > 0 else 0
            },
            "processing_metrics": self.processing_metrics
        }

    def get_alert_status(self, alert_id: str) -> Optional[AlertStatus]:
        """Get current status of an alert."""
        if alert_id in self.active_alerts:
            return self.active_alerts[alert_id].status
        return None

    def update_thresholds(self, new_thresholds: Dict[str, float]) -> bool:
        """
        Update alert thresholds dynamically with validation.

        Args:
            new_thresholds: Dictionary of threshold updates

        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create updated configuration
            updated_config = self.alert_config.copy()
            updated_config.update(new_thresholds)

            # Validate the updated configuration
            validation_result = self._validate_configuration(updated_config)

            if not validation_result['is_valid']:
                logger.error(f"Threshold update validation failed: {'; '.join(validation_result['errors'])}")
                return False

            # Apply the validated configuration
            self.alert_config = validation_result['sanitized_config']

            # Log warnings if any
            for warning in validation_result['warnings']:
                logger.warning(f"Threshold update warning: {warning}")

            logger.info(f"Successfully updated thresholds: {new_thresholds}")
            return True

        except Exception as e:
            logger.error(f"Failed to update thresholds: {e}")
            return False

    def update_notification_config(self, new_config: Dict[str, Any]) -> bool:
        """
        Update notification configuration dynamically with validation.

        Args:
            new_config: Dictionary of notification configuration updates

        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create updated configuration
            updated_config = self.alert_config.copy()
            updated_config.update(new_config)

            # Validate the updated configuration
            validation_result = self._validate_configuration(updated_config)

            if not validation_result['is_valid']:
                logger.error(f"Notification config update validation failed: {'; '.join(validation_result['errors'])}")
                return False

            # Apply the validated configuration
            self.alert_config = validation_result['sanitized_config']

            # Log warnings if any
            for warning in validation_result['warnings']:
                logger.warning(f"Notification config update warning: {warning}")

            logger.info(f"Successfully updated notification configuration: {new_config}")
            return True

        except Exception as e:
            logger.error(f"Failed to update notification configuration: {e}")
            return False

    def get_current_configuration(self) -> Dict[str, Any]:
        """
        Get current configuration with sensitive data masked.

        Returns:
            Current configuration dictionary with sensitive values masked
        """
        config_copy = self.alert_config.copy()

        # Mask sensitive configuration values
        sensitive_keys = ['webhook_url', 'email_recipients']
        for key in sensitive_keys:
            if key in config_copy and config_copy[key]:
                if key == 'email_recipients':
                    # Mask email addresses
                    config_copy[key] = [self._mask_email(email) for email in config_copy[key]]
                elif key == 'webhook_url':
                    # Mask webhook URL
                    config_copy[key] = self._mask_url(config_copy[key])

        return config_copy

    def _mask_email(self, email: str) -> str:
        """Mask email address for logging/display."""
        if '@' in email:
            local, domain = email.split('@', 1)
            if len(local) > 2:
                masked_local = local[:2] + '*' * (len(local) - 2)
            else:
                masked_local = '*' * len(local)
            return f"{masked_local}@{domain}"
        return '*' * len(email)

    def _mask_url(self, url: str) -> str:
        """Mask URL for logging/display."""
        if '://' in url:
            protocol, rest = url.split('://', 1)
            if '/' in rest:
                host, path = rest.split('/', 1)
                return f"{protocol}://{host}/***"
            else:
                return f"{protocol}://{rest}"
        return '***'

    def reload_configuration_from_environment(self) -> bool:
        """
        Reload configuration from environment variables.

        Returns:
            True if reload was successful, False otherwise
        """
        try:
            # Load fresh environment configuration
            env_config = self._load_environment_config()

            if not env_config:
                logger.info("No environment configuration found to reload")
                return True

            # Update configuration with environment variables
            return self.update_notification_config(env_config)

        except Exception as e:
            logger.error(f"Failed to reload configuration from environment: {e}")
            return False

    async def check_channel_health(self) -> Dict[NotificationChannel, bool]:
        """
        Check health status of all configured notification channels.

        Performs comprehensive health checks including:
        - Channel availability and connectivity
        - Configuration validation
        - Recent delivery success rates
        - Error rate analysis

        Returns:
            Dictionary mapping channels to their health status
        """
        health_status = {}

        for channel_name in self.alert_config.get('enabled_channels', ['mqtt', 'http']):
            try:
                channel = NotificationChannel(channel_name)

                # Perform channel-specific health checks
                is_healthy = await self._perform_channel_health_check(channel)
                health_status[channel] = is_healthy

                if not is_healthy:
                    logger.warning(f"Health check failed for channel {channel.value}")

            except Exception as e:
                logger.error(f"Health check failed for {channel_name}: {e}")
                try:
                    health_status[NotificationChannel(channel_name)] = False
                except ValueError:
                    logger.error(f"Invalid channel name during health check: {channel_name}")

        # Log overall health status
        healthy_channels = sum(1 for status in health_status.values() if status)
        total_channels = len(health_status)

        logger.info(f"Channel health check completed: {healthy_channels}/{total_channels} channels healthy")

        return health_status

    async def _perform_channel_health_check(self, channel: NotificationChannel) -> bool:
        """
        Perform detailed health check for a specific channel.

        Args:
            channel: Notification channel to check

        Returns:
            True if channel is healthy, False otherwise
        """
        try:
            # Check configuration validity
            if not self._validate_channel_configuration(channel):
                return False

            # Check recent delivery success rate
            recent_success_rate = self._calculate_recent_success_rate(channel)
            if recent_success_rate < 0.8:  # Less than 80% success rate
                logger.warning(f"Channel {channel.value} has low success rate: {recent_success_rate:.1%}")
                return False

            # Check for circuit breaker status
            error_tracking = getattr(self, 'error_tracking', {})
            channel_failures = error_tracking.get('channel_failures', {}).get(channel, 0)
            max_failures = getattr(self, 'error_recovery_config', {}).get('max_consecutive_failures', 5)

            if channel_failures >= max_failures:
                logger.warning(f"Channel {channel.value} has too many consecutive failures: {channel_failures}")
                return False

            # Perform connectivity test (placeholder for actual implementation)
            connectivity_ok = await self._test_channel_connectivity(channel)

            return connectivity_ok

        except Exception as e:
            logger.error(f"Error during health check for {channel.value}: {e}")
            return False

    def _validate_channel_configuration(self, channel: NotificationChannel) -> bool:
        """Validate configuration for a specific channel."""
        if channel == NotificationChannel.EMAIL:
            recipients = self.alert_config.get('email_recipients', [])
            return bool(recipients) and all(self._validate_email(email) for email in recipients)

        elif channel == NotificationChannel.MQTT:
            broker = self.alert_config.get('mqtt_broker')
            topic = self.alert_config.get('mqtt_topic')
            return bool(broker) and self._validate_mqtt_broker(broker) and bool(topic)

        elif channel == NotificationChannel.HTTP:
            webhook_url = self.alert_config.get('webhook_url')
            return bool(webhook_url) and self._validate_url(webhook_url)

        return False

    def _calculate_recent_success_rate(self, channel: NotificationChannel, lookback_count: int = 20) -> float:
        """Calculate recent success rate for a channel."""
        # Get recent deliveries for this channel
        recent_deliveries = [
            delivery for delivery in self.notification_history[-lookback_count:]
            if delivery.channel == channel
        ]

        if not recent_deliveries:
            return 1.0  # No recent deliveries, assume healthy

        successful_deliveries = sum(1 for delivery in recent_deliveries if delivery.status == "success")
        return successful_deliveries / len(recent_deliveries)

    async def _test_channel_connectivity(self, channel: NotificationChannel) -> bool:
        """Test connectivity for a specific channel."""
        try:
            # In a real implementation, this would perform actual connectivity tests
            # For now, we'll simulate based on configuration validity

            if channel == NotificationChannel.EMAIL:
                # Would test SMTP connectivity
                return True
            elif channel == NotificationChannel.MQTT:
                # Would test MQTT broker connectivity
                return True
            elif channel == NotificationChannel.HTTP:
                # Would test webhook endpoint connectivity
                return True

            return False

        except Exception as e:
            logger.error(f"Connectivity test failed for {channel.value}: {e}")
            return False

    def get_comprehensive_health_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive health report for monitoring systems.

        Returns:
            Detailed health report with metrics and status information
        """
        try:
            # Get basic metrics
            basic_metrics = self.get_metrics()

            # Calculate additional health metrics
            total_alerts = len(self.active_alerts)
            pending_alerts = len([a for a in self.active_alerts.values() if a.status == AlertStatus.PENDING])
            failed_alerts = len([a for a in self.active_alerts.values() if a.status == AlertStatus.FAILED])

            # Calculate notification metrics
            total_notifications = len(self.notification_history)
            recent_notifications = self.notification_history[-50:]  # Last 50 notifications
            recent_success_rate = 0.0

            if recent_notifications:
                successful = sum(1 for n in recent_notifications if n.status == "success")
                recent_success_rate = successful / len(recent_notifications)

            # Calculate average processing times
            recent_deliveries_with_time = [
                d for d in recent_notifications
                if d.delivery_time_ms is not None
            ]

            avg_delivery_time = 0.0
            if recent_deliveries_with_time:
                total_time = sum(d.delivery_time_ms for d in recent_deliveries_with_time)
                avg_delivery_time = total_time / len(recent_deliveries_with_time)

            # System health indicators
            error_tracking = getattr(self, 'error_tracking', {})
            circuit_breaker_open = error_tracking.get('circuit_breaker_open', False)
            consecutive_failures = error_tracking.get('consecutive_failures', 0)

            health_report = {
                "timestamp": datetime.now().isoformat(),
                "company_name": self.company_name,
                "system_status": {
                    "overall_health": "healthy" if not circuit_breaker_open and consecutive_failures < 3 else "degraded",
                    "circuit_breaker_open": circuit_breaker_open,
                    "consecutive_failures": consecutive_failures
                },
                "alert_metrics": {
                    "total_active_alerts": total_alerts,
                    "pending_alerts": pending_alerts,
                    "failed_alerts": failed_alerts,
                    "alert_failure_rate": failed_alerts / total_alerts if total_alerts > 0 else 0.0
                },
                "notification_metrics": {
                    "total_notifications": total_notifications,
                    "recent_success_rate": recent_success_rate,
                    "average_delivery_time_ms": avg_delivery_time,
                    "notifications_last_hour": len([
                        n for n in recent_notifications
                        if (datetime.now() - n.timestamp).total_seconds() < 3600
                    ])
                },
                "performance_metrics": basic_metrics,
                "configuration_status": {
                    "enabled_channels": self.alert_config.get('enabled_channels', []),
                    "total_thresholds_configured": len([
                        k for k in self.alert_config.keys()
                        if k.endswith('_threshold')
                    ])
                }
            }

            return health_report

        except Exception as e:
            logger.error(f"Failed to generate health report: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "company_name": self.company_name,
                "error": f"Health report generation failed: {str(e)}",
                "system_status": {"overall_health": "unknown"}
            }

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance and operational metrics.

        Returns:
            Dictionary containing detailed metrics for monitoring and analysis
        """
        try:
            # Calculate time-based metrics
            current_time = datetime.now()

            # Alert age metrics
            alert_ages = []
            for alert in self.active_alerts.values():
                age_hours = (current_time - alert.timestamp).total_seconds() / 3600
                alert_ages.append(age_hours)

            avg_alert_age = sum(alert_ages) / len(alert_ages) if alert_ages else 0.0
            max_alert_age = max(alert_ages) if alert_ages else 0.0

            # Notification delivery metrics
            recent_notifications = self.notification_history[-100:]  # Last 100 notifications
            delivery_times = [
                n.delivery_time_ms for n in recent_notifications
                if n.delivery_time_ms is not None
            ]

            avg_delivery_time = sum(delivery_times) / len(delivery_times) if delivery_times else 0.0
            max_delivery_time = max(delivery_times) if delivery_times else 0.0

            # Channel-specific metrics
            channel_metrics = {}
            for channel in NotificationChannel:
                channel_notifications = [n for n in recent_notifications if n.channel == channel]
                if channel_notifications:
                    successful = sum(1 for n in channel_notifications if n.status == "success")
                    channel_metrics[channel.value] = {
                        "total_notifications": len(channel_notifications),
                        "successful_notifications": successful,
                        "success_rate": successful / len(channel_notifications),
                        "average_delivery_time_ms": sum(
                            n.delivery_time_ms for n in channel_notifications
                            if n.delivery_time_ms is not None
                        ) / len([n for n in channel_notifications if n.delivery_time_ms is not None]) if any(n.delivery_time_ms for n in channel_notifications) else 0.0
                    }

            # Alert type distribution
            alert_type_counts = {}
            for alert_type in AlertType:
                count = len([a for a in self.active_alerts.values() if a.alert_type == alert_type])
                alert_type_counts[alert_type.value] = count

            # Severity distribution
            severity_counts = {}
            for severity in AlertSeverity:
                count = len([a for a in self.active_alerts.values() if a.severity == severity])
                severity_counts[severity.value] = count

            # Error tracking metrics
            error_tracking = getattr(self, 'error_tracking', {})

            return {
                # Basic processing metrics
                **self.processing_metrics,

                # Alert metrics
                "alert_metrics": {
                    "active_alerts": len(self.active_alerts),
                    "average_alert_age_hours": avg_alert_age,
                    "maximum_alert_age_hours": max_alert_age,
                    "alert_type_distribution": alert_type_counts,
                    "severity_distribution": severity_counts
                },

                # Notification metrics
                "notification_metrics": {
                    "total_notification_history": len(self.notification_history),
                    "recent_notifications_analyzed": len(recent_notifications),
                    "average_delivery_time_ms": avg_delivery_time,
                    "maximum_delivery_time_ms": max_delivery_time,
                    "channel_metrics": channel_metrics
                },

                # System health metrics
                "system_health": {
                    "circuit_breaker_open": error_tracking.get('circuit_breaker_open', False),
                    "consecutive_failures": error_tracking.get('consecutive_failures', 0),
                    "last_failure_time": error_tracking.get('last_failure_time').isoformat() if error_tracking.get('last_failure_time') else None,
                    "channel_failure_counts": {
                        channel.value: count for channel, count in error_tracking.get('channel_failures', {}).items()
                    }
                },

                # Configuration metrics
                "configuration": {
                    "company_name": self.company_name,
                    "enabled_channels": self.alert_config.get('enabled_channels', []),
                    "total_configured_thresholds": len([
                        k for k in self.alert_config.keys()
                        if k.endswith('_threshold')
                    ]),
                    "key_thresholds": {
                        "shortage_threshold": self.alert_config.get('shortage_threshold'),
                        "revenue_decline_threshold": self.alert_config.get('revenue_decline_threshold'),
                        "profit_margin_threshold": self.alert_config.get('profit_margin_threshold')
                    }
                },

                # Timestamp for metrics collection
                "metrics_timestamp": current_time.isoformat(),
                "uptime_hours": (current_time - datetime.fromtimestamp(0)).total_seconds() / 3600  # Placeholder for actual uptime
            }

        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return {
                "error": f"Metrics collection failed: {str(e)}",
                "basic_metrics": self.processing_metrics,
                "active_alerts": len(self.active_alerts),
                "notification_history_count": len(self.notification_history),
                "metrics_timestamp": datetime.now().isoformat()
            }

    def _is_circuit_breaker_open(self, channel: NotificationChannel) -> bool:
        """
        Check if circuit breaker is open for a notification channel.

        Args:
            channel: Notification channel to check

        Returns:
            True if circuit breaker is open (channel should be skipped)
        """
        if not hasattr(self, '_circuit_breakers'):
            self._circuit_breakers = {}

        channel_key = channel.value
        if channel_key not in self._circuit_breakers:
            self._circuit_breakers[channel_key] = {
                'failure_count': 0,
                'last_failure_time': None,
                'is_open': False
            }

        breaker = self._circuit_breakers[channel_key]

        # Check if circuit breaker should be reset (after 5 minutes)
        if breaker['is_open'] and breaker['last_failure_time']:
            time_since_failure = (datetime.now() - breaker['last_failure_time']).total_seconds()
            if time_since_failure > 300:  # 5 minutes
                logger.info(f"Resetting circuit breaker for {channel_key}")
                breaker['is_open'] = False
                breaker['failure_count'] = 0

        return breaker['is_open']

    def _record_notification_failure(self, channel: NotificationChannel, error_message: str):
        """
        Record a notification failure and potentially open circuit breaker.

        Args:
            channel: Notification channel that failed
            error_message: Error message from the failure
        """
        if not hasattr(self, '_circuit_breakers'):
            self._circuit_breakers = {}

        channel_key = channel.value
        if channel_key not in self._circuit_breakers:
            self._circuit_breakers[channel_key] = {
                'failure_count': 0,
                'last_failure_time': None,
                'is_open': False
            }

        breaker = self._circuit_breakers[channel_key]
        breaker['failure_count'] += 1
        breaker['last_failure_time'] = datetime.now()

        # Open circuit breaker after 3 consecutive failures
        if breaker['failure_count'] >= 3 and not breaker['is_open']:
            breaker['is_open'] = True
            logger.warning(f"Circuit breaker opened for {channel_key} after {breaker['failure_count']} failures")

        # Special handling for authentication errors (immediate circuit breaker)
        if 'authentication' in error_message.lower() or 'credentials' in error_message.lower():
            breaker['is_open'] = True
            logger.warning(f"Circuit breaker opened for {channel_key} due to authentication failure")


def create_alert_manager_agent(company_name: str = "test_company", alert_config: Dict[str, Any] = None) -> AlertManagerAgent:
    """
    Factory function to create AlertManagerAgent instances following reference implementation pattern.

    This function creates AlertManagerAgent instances that use structured prompt generation
    aligned with the reference implementation from agent_develop/notification/client.py.
    The agent maintains compatibility with existing MCP financial analyzer workflows while
    adopting the standardized prompt structure and schema patterns.

    Args:
        company_name: Name of the company being analyzed
        alert_config: Configuration dictionary for alert settings including thresholds,
                     notification channels, and delivery configuration

    Returns:
        Configured AlertManagerAgent instance with structured prompt and enhanced capabilities
    """
    agent = AlertManagerAgent(company_name=company_name, alert_config=alert_config)

    # Ensure LLM is initialized for MCP operations
    async def initialize_agent_llm():
        """Initialize the agent's LLM for MCP operations."""
        if hasattr(agent, 'ensure_llm_initialized'):
            await agent.ensure_llm_initialized()

    # Add initialization method to agent
    agent.initialize_llm = initialize_agent_llm

    return agent


# Legacy factory function for backward compatibility - now uses structured prompt
def create_enhanced_alert_manager_agent(company_name: str = "test_company", alert_config: Dict[str, Any] = None) -> BaseAgentWrapper:
    """
    Create an alert manager agent following the reference implementation pattern.

    This agent integrates with the agent_develop alert-notification server to evaluate
    alert conditions and send notifications through multiple channels (email, MQTT, HTTP).
    Now uses structured prompt generation aligned with the reference implementation.

    Args:
        company_name: Name of the company being analyzed
        alert_config: Configuration dictionary containing alert thresholds and notification settings

    Returns:
        BaseAgentWrapper configured for alert management with structured prompt
    """

    # Build default configuration if not provided
    if alert_config is None:
        alert_config = {}

    # Use the static method to build structured instruction following reference pattern
    instruction = AlertManagerAgent._build_instruction_static(company_name, alert_config)

    return create_enhanced_agent(
        name=f"alert_manager_{company_name.lower().replace(' ', '_')}",
        instruction=instruction,
        server_names=["alert-notification"],
        model="Qwen/Qwen3-32B"  # Use vLLM Qwen model for enhanced reasoning
    )