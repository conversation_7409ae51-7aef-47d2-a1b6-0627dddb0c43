"""Tool for adding two numbers."""
import requests
from typing import Dict, Any, Union
from pydantic import Field, BaseModel

from notification.interfaces.notification import NotificationResponse, Notification

url = "http://localhost:5000/alert"

class HttpInput(BaseModel):
    """Input schema for the Notification tool."""

    subject: str = Field(description="The subject of the notification", examples="CPU material shortage notification")
    content: str = Field(description="The content of the notification message", examples="The CPU material is running low, please check the inventory.")


class HttpOutput(BaseModel):
    """Output schema for the Notification tool."""
    message: str = Field(description="The message indicating the result of the operation", examples="Notification sent successfully.")
    error: Union[str, None] = Field(default=None, description="An error message if the operation failed.")


class HttpTool(Notification):
    """Tool that sends HTTP alerts."""

    name = "HttpNotification"
    description = "Sends an HTTP alert to a specified endpoint."
    input_model = HttpInput
    output_model = HttpOutput

    def get_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this tool."""
        return {
            "name": self.name,
            "description": self.description,
            "input": self.input_model.model_json_schema(),
            "output": self.output_model.model_json_schema(),
        }

    async def execute(self, input_data: HttpInput) -> NotificationResponse:
        """
        Send notification via HTTP POST request.

        Args:
            input_data (dict) = {
                subject (str): The subject of the alert.
                content (str): The content of the alert message.
            }
        Returns:
            NotificationResponse (str): A response indicating the result of the operation.
        """
        response = requests.post(
            url,
            json={
                "subject": input_data.subject,
                "body": input_data.content
            }
        )
        if response.status_code == 200:
            return NotificationResponse.from_text("✅ HTTP notification sent successfully")
        else:
            raise Exception(f"❌ Failed to send HTTP notification: {response.status_code}")
