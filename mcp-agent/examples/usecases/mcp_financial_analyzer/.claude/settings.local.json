{"permissions": {"allow": ["Bash(git commit:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(timeout:*)", "mcp__serena__list_dir", "mcp__serena__check_onboarding_performed", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "Bash(git push:*)", "<PERSON><PERSON>(curl:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(PYTHONPATH=/merge/dev_agent/agent_develop python -m notification.server --host 0.0.0.0 --port 6969)", "Bash(PYTHONPATH=/merge/dev_agent/agent_develop python notification/client.py)", "Bash(kill:*)", "Bash(PYTHONPATH=/merge/dev_agent/agent_develop python -m notification.server --host 0.0.0.0 --port 6972)"], "additionalDirectories": ["/merge/dev_agent/agent_develop"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["ask-repo-agent", "search-repo-docs"]}